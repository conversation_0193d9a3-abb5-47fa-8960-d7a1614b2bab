<script lang="ts">
	import Button from '$lib/components/ui/button/button.svelte';
	import { getCurrentOrgId } from '$lib/current-org.svelte.js';

	const { data } = $props();

	const orgContext = getCurrentOrgId();
	const currentOrg = orgContext.getCurrentOrg();
</script>

<div class="flex flex-col gap-8">
	<h1 class="mb-0">Welcome to Cost Atlas</h1>

	<section>
		<h2 class="my-4 text-2xl">Your Recent Projects</h2>
		{#if data.projects.length === 0}
			<div class="rounded-lg border border-dashed p-8 text-center">
				<p class="text-muted-foreground">Choose a client to get your first project started.</p>
			</div>
		{:else}
			<div class="rounded-lg border">
				<table class="w-full">
					<thead>
						<tr class="bg-muted/50 border-b">
							<th class="px-4 py-3 text-left font-medium">Name</th>
							<th class="px-4 py-3 text-left font-medium">Description</th>
							<th class="px-4 py-3 text-left font-medium">Client</th>
							<th class="px-4 py-3 text-left font-medium">Created</th>
							<th class="px-4 py-3 text-left font-medium">Updated</th>
							<th class="px-4 py-3 text-right font-medium">Actions</th>
						</tr>
					</thead>
					<tbody>
						{#each data.projects as project (project.project_id)}
							<tr class="hover:bg-muted/50 border-b last:border-b-0">
								<td class="px-4 py-3 font-medium">
									<a
										href="/org/{encodeURIComponent(
											project.client.organization.name,
										)}/clients/{encodeURIComponent(
											project.client.name,
										)}/projects/{encodeURIComponent(project.name)}"
										class="hover:text-primary hover:underline"
									>
										{project.name}
									</a>
								</td>
								<td class="text-muted-foreground px-4 py-3">
									{project.description || '-'}
								</td>
								<td class="text-muted-foreground px-2">
									<a
										class="rounded-md px-2 py-3 hover:bg-gray-200 hover:underline"
										href="/org/{encodeURIComponent(
											project.client.organization.name,
										)}/clients/{encodeURIComponent(project.client.name)}"
										>{project.client.name || '-'}</a
									>
								</td>
								<td class="text-muted-foreground px-4 py-3">
									{new Date(project.created_at).toLocaleDateString()}
								</td>
								<td class="text-muted-foreground px-4 py-3">
									{new Date(project.updated_at).toLocaleDateString()}
								</td>
								<td class="px-4 py-3 text-right">
									<Button
										href="/org/{encodeURIComponent(
											project.client.organization.name,
										)}/clients/{encodeURIComponent(
											project.client.name,
										)}/projects/{encodeURIComponent(project.name)}/edit"
										variant="outline"
										size="sm"
									>
										Edit
									</Button>
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>
		{/if}
	</section>

	<section>
		<h2 class="my-4 text-2xl">Your Recent Clients</h2>
		{#if data.clients.length === 0}
			<div class="rounded-lg border border-dashed p-8 text-center">
				<p class="text-muted-foreground">
					No clients found. Create your first client to get started.
				</p>
			</div>
		{:else}
			<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
				<!-- TODO: switch to match Clients table on /clients route? -->
				{#each data.clients as client (client.client_id)}
					<div class="rounded-lg border p-4 shadow-xs">
						<div class="flex items-center justify-between">
							<a
								href="/org/{encodeURIComponent(
									client.organization.name,
								)}/clients/{encodeURIComponent(client.name)}"
							>
								<h2 class="text-xl font-semibold">{client.name}</h2>
							</a>
							<Button
								href="/org/{encodeURIComponent(
									client.organization.name,
								)}/clients/{encodeURIComponent(client.name)}/edit"
								variant="outline"
								size="sm">Edit</Button
							>
						</div>
						{#if client.description}
							<p class="text-muted-foreground mt-2">{client.description}</p>
						{/if}
						<div class="mt-2 flex flex-wrap gap-2">
							{#if client.client_url}
								<a
									href={client.client_url}
									target="_blank"
									rel="noopener noreferrer"
									class="inline-block text-sm text-blue-600 hover:underline"
								>
									Visit Website
								</a>
							{/if}
							{#if client.internal_url}
								<a
									href={client.internal_url}
									target="_blank"
									rel="noopener noreferrer"
									class="inline-block text-sm text-blue-600 hover:underline"
								>
									{client.internal_url_description || 'Internal Site'}
								</a>
							{/if}
						</div>
					</div>
				{/each}
			</div>
			{#if currentOrg?.name}
				<div class="mt-4">
					<a
						class="text-blue-500 hover:underline"
						href="/org/{encodeURIComponent(currentOrg.name)}/clients"
					>
						View All Clients &rarr;
					</a>
				</div>
			{/if}
		{/if}
	</section>
</div>
