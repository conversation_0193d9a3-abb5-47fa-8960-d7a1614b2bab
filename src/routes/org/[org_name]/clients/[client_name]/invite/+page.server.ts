import { fail } from '@sveltejs/kit';
import { redirect } from 'sveltekit-flash-message/server';
import { zod } from 'sveltekit-superforms/adapters';
import { message, superValidate } from 'sveltekit-superforms/server';
import type { PageServerLoad, Actions } from './$types';
import { clientInviteSchema } from '$lib/schemas/client';
import { requireUser } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals, params, cookies, depends }) => {
	depends('client:invite');
	await requireUser(cookies);

	const { supabase } = locals;
	const { client_name, org_name } = params;

	// Fetch the client data
	const { data: client, error: clientError } = await supabase
		.from('client')
		.select('*, organization(org_id, name)')
		.eq('organization.name', org_name)
		.eq('name', client_name)
		.limit(1)
		.maybeSingle();

	if (clientError || !client) {
		console.error('Error fetching client:', clientError);
		return redirect(303, `/org/${encodeURIComponent(org_name)}/clients`);
	}

	// Check if user has permission to manage team
	const { data: isClientAdmin } = await supabase.rpc('is_client_admin', {
		client_id_param: client.client_id,
	});

	if (!isClientAdmin) {
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client.name)}`,
			{ type: 'error', message: 'You do not have permission to manage team members' },
			cookies,
		);
	}

	// Get client members using the security definer function
	const { data: clientMembers, error: membersError } = await supabase.rpc('get_client_members', {
		_client_name: client_name,
	});

	if (membersError) {
		console.error('Error fetching client members:', membersError);
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client.name)}`,
			{ type: 'error', message: 'Error fetching client members' },
			cookies,
		);
	}

	// filter client members to deduplicate by user_id and merge the roles to a single array
	const uniqueMembers = new Map<string, (typeof clientMembers)[0] & { roles: string[] }>();
	clientMembers?.forEach((member) => {
		if (!uniqueMembers.has(member.user_id)) {
			uniqueMembers.set(member.user_id, {
				...member,
				roles: [member.role],
			});
		} else {
			const existingMember = uniqueMembers.get(member.user_id);
			existingMember!.roles.push(member.role);
			uniqueMembers.set(member.user_id, existingMember!);
		}
	});
	const members = Array.from(uniqueMembers.values());

	// Create and validate the form
	const form = await superValidate(zod(clientInviteSchema));

	const { data: invites, error: invitesError } = await supabase
		.from('invite')
		.select('*')
		.eq('resource_id', client.client_id)
		.eq('resource_type', 'client')
		.eq('status', 'pending')
		.order('created_at', { ascending: false });

	if (invitesError) {
		console.error('Error fetching invites:', invitesError);
		return redirect(
			303,
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client.name)}`,
		);
	}

	return {
		client,
		members,
		form,
		invites: invites ?? [],
	};
};

export const actions: Actions = {
	invite: async ({ request, locals, params, fetch, cookies }) => {
		const form = await superValidate(request, zod(clientInviteSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		await requireUser(cookies);

		const { supabase } = locals;
		const { client_name, org_name } = params;

		// Fetch client id
		const { data: client, error: clientError } = await supabase
			.from('client')
			.select('client_id, organization(org_id, name)')
			.eq('organization.name', org_name)
			.eq('name', client_name)
			.eq('client_id', form.data.client_id)
			.limit(1)
			.single();
		if (clientError || !client) {
			console.error('Error fetching client:', clientError);
			return fail(404, { form, error: 'Client not found' });
		}

		// Check if user has permission to manage team
		const { data: isClientAdmin } = await supabase.rpc('is_client_admin', {
			client_id_param: client.client_id,
		});

		if (!isClientAdmin) {
			return redirect(
				303,
				`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
			);
		}

		// Find user by email
		const { data: userProfile, error: findError } = await supabase
			.from('profile')
			.select('user_id')
			.eq('email', form.data.email)
			.maybeSingle();
		if (findError) {
			console.error('Error finding user:', findError);
			return message(form, {
				text: 'Something went wrong looking up that email address. Please try again.',
				type: 'error',
			});
		}

		if (!userProfile) {
			// User not found, send invitation
			const data = await fetch('/api/invites', {
				method: 'POST',
				body: JSON.stringify({
					resourceType: 'client',
					resourceId: client.client_id,
					role: form.data.role,
					inviteeEmail: form.data.email,
					expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
				}),
			});
			if (!data.ok) {
				console.error('Error sending invitation:', data);
				return message(form, {
					text: 'Something went wrong sending that invitation. Please try again.',
					type: 'error',
				});
			} else {
				return message(form, { text: 'Invitation sent', type: 'success' });
			}
		}

		// User found, add to membership table
		// TODO: don't auto add users - send an invitation instead, requires a new page or inbox for pending invites
		const { error: memberError } = await supabase.from('membership').insert({
			entity_id: client.client_id,
			entity_type: 'client',
			user_id: userProfile.user_id,
			role: form.data.role,
		});

		if (memberError) {
			console.error('Error adding user to client:', memberError);
			return message(form, { text: 'Something went wrong', type: 'error' });
		}

		return message(form, { text: 'User added to client', type: 'success' });
	},

	remove: async ({ request, locals, params }) => {
		const data = await request.formData();
		const permissionId = data.get('permissionId')?.toString();

		if (!permissionId) {
			return fail(400, { error: 'Invalid permission ID' });
		}

		const { supabase } = locals;
		const { client_name, org_name } = params;

		// Get the client_id first
		const { data: client, error: clientError } = await supabase
			.from('client')
			.select('client_id, organization(org_id, name)')
			.eq('organization.name', org_name)
			.eq('name', client_name)
			.limit(1)
			.single();

		if (clientError || !client) {
			console.error('Error fetching client:', clientError);
			return fail(404, { error: 'Client not found' });
		}

		// Check if user has permission to manage team
		const { data: isClientAdmin } = await supabase.rpc('is_client_admin', {
			client_id_param: client.client_id,
		});

		console.log({ isClientAdmin, client_id: client.client_id, permissionId: Number(permissionId) });

		if (!isClientAdmin) {
			return fail(403, { error: 'You do not have permission to manage team members' });
		}

		// Delete the membership
		const deletedPermissionResponse = await supabase
			.from('membership')
			.delete()
			.eq('membership_id', Number(permissionId));

		console.log({ deletedPermissionResponse, permissionId });

		// if (error) {
		// 	console.error('Error removing permission:', error);
		// 	return fail(500, { error: 'Failed to remove user from client' });
		// }

		return {
			success: true,
		};
	},
};
