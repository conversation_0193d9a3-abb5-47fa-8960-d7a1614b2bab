<script lang="ts">
	import type { PageData } from './$types';
	import { Button } from '$lib/components/ui/button';
	import { superForm } from 'sveltekit-superforms';

	// Import shadcn components
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Form from '$lib/components/ui/form';
	import { toast } from 'svelte-sonner';
	import { page } from '$app/state';

	const { data }: { data: PageData } = $props();
	const form = superForm(data.form, {
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
	const { form: formData, enhance: superEnhance } = form;

	function getLibraryName(libraryId: number): string {
		const library = data.wbsLibraries.find((lib) => lib.wbs_library_id === libraryId);
		return library ? library.name : 'Unknown Library';
	}
</script>

<div class="container max-w-(--breakpoint-xl) py-8">
	<div class="mb-6">
		<h1 class="text-3xl font-bold">Edit WBS Item</h1>
		<p class="mt-1 text-slate-600">Update the details for this WBS item</p>
	</div>

	<div class="rounded-lg border bg-white p-6">
		<form method="POST" action="?/updateItem" class="space-y-6" use:superEnhance>
			<input type="hidden" name="wbs_library_item_id" value={data.wbsItem.wbs_library_item_id} />
			<input type="hidden" name="client_id" value={data.wbsItem.client_id} />
			<input type="hidden" name="project_id" value={data.wbsItem.project_id} />
			<input type="hidden" name="wbs_library_id" value={data.wbsItem.wbs_library_id} />

			<div>
				<div class="mb-4 rounded-md bg-slate-50 p-4">
					<div class="grid grid-cols-2 gap-4">
						<div>
							<span class="block text-sm text-slate-500">Library</span>
							<span class="font-medium">{getLibraryName(data.wbsItem.wbs_library_id)}</span>
						</div>
						{#if data.wbsItem.parent_item_id}
							<div>
								<span class="block text-sm text-slate-500">Parent Item</span>
								<span class="font-medium">{data.wbsItem.parent_item_id}</span>
							</div>
						{/if}
					</div>
				</div>
			</div>

			<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
				<Form.Field {form} name="code">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Code</Form.Label>
							<Input {...props} value={$formData.code ?? ''} required />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="level">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Level</Form.Label>
							<Input {...props} type="number" value={$formData.level ?? ''} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			</div>

			<Form.Field {form} name="description">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Description</Form.Label>
						<Input {...props} value={$formData.description ?? ''} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="cost_scope">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Scope Details</Form.Label>
						<Textarea {...props} value={$formData.cost_scope ?? ''} rows={4} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<div class="flex justify-between space-x-2 pt-4">
				<div>
					<Button
						type="button"
						variant="outline"
						class="bg-red-50 text-red-600 hover:bg-red-100"
						onclick={() => {
							// Submit a form to the delete action
							const form = document.createElement('form');
							form.method = 'POST';
							form.action = '?/deleteItem';
							document.body.appendChild(form);
							form.submit();
						}}
					>
						Delete WBS Item
					</Button>
				</div>

				<div class="flex space-x-2">
					<Button
						href="/org/{encodeURIComponent(page.params.org_name)}/clients/{encodeURIComponent(
							data.client.name,
						)}/wbs"
						type="button"
						variant="outline">Cancel</Button
					>
					<Form.Button>Save Changes</Form.Button>
				</div>
			</div>
		</form>
	</div>
</div>
