<script lang="ts">
	import type { PageData } from './$types';
	import { Button } from '$lib/components/ui/button';
	import { superForm } from 'sveltekit-superforms';
	import WbsTreeItem from '$lib/components/wbs-tree-item.svelte';

	// Import shadcn components
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Form from '$lib/components/ui/form';
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import { toast } from 'svelte-sonner';
	import CheckIcon from 'phosphor-svelte/lib/Check';
	import CaretUpDownIcon from 'phosphor-svelte/lib/CaretUpDown';
	import { buttonVariants } from '$lib/components/ui/button';
	import { cn } from '$lib/utils';
	import { tick } from 'svelte';
	import { useId } from 'bits-ui';
	import { page } from '$app/state';

	const { data }: { data: PageData } = $props();
	const form = superForm(data.form, {
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
	const { form: formData, enhance: superEnhance, reset } = form;

	// State variables
	let showAddForm = $state(false);
	let expandedClientCategories = $state<Record<number, boolean>>({});
	let expandedProjectCategories = $state<Record<number, boolean>>({});
	let expandedMainCategories = $state<Record<number, boolean>>({});
	let openParentCombobox = $state(false);
	const triggerId = useId();

	// Check if any items have scope defined to show the column
	const showClientScopeColumn = $derived(data.clientItems.some((item) => item.cost_scope));
	const showProjectScopeColumn = $derived(data.projectItems.some((item) => item.cost_scope));
	const showMainScopeColumn = $derived(data.mainWbsLibraryItems.some((item) => item.cost_scope));

	// Create a combined list of parent options
	const parentOptions = $derived.by(() => {
		const options: {
			label: string;
			value: string;
			source: 'client' | 'project' | 'main';
			level: number;
		}[] = [];

		// Add client items as potential parents
		data.clientItems.forEach((item) => {
			options.push({
				label: `${item.code}: ${item.description}`,
				value: String(item.wbs_library_item_id),
				source: 'client',
				level: item.level || 1,
			});
		});

		// Add project items as potential parents
		data.projectItems.forEach((item) => {
			options.push({
				label: `${item.code}: ${item.description}`,
				value: String(item.wbs_library_item_id),
				source: 'project',
				level: item.level || 1,
			});
		});

		// Add main WBS library items as potential parents
		data.mainWbsLibraryItems.forEach((item) => {
			options.push({
				label: `${item.code}: ${item.description}`,
				value: String(item.wbs_library_item_id),
				source: 'main',
				level: item.level || 1,
			});
		});

		// Filter based on the current level selected
		const currentLevel = $formData.level || 1;
		if (currentLevel > 1) {
			// For level greater than 1, only show parent items of level (currentLevel - 1)
			return options.filter((item) => item.level === currentLevel - 1);
		}

		return options;
	});

	function getParentName(parentId: string | null | undefined): string {
		if (!parentId) return 'None';

		const clientItem = data.clientItems.find((item) => item.wbs_library_item_id === parentId);
		if (clientItem) {
			return `${clientItem.code}: ${clientItem.description}`;
		}

		const projectItem = data.projectItems.find((item) => item.wbs_library_item_id === parentId);
		if (projectItem) {
			return `${projectItem.code}: ${projectItem.description}`;
		}

		const mainItem = data.mainWbsLibraryItems.find((item) => item.wbs_library_item_id === parentId);
		if (mainItem) {
			return `${mainItem.code}: ${mainItem.description}`;
		}

		return 'Unknown';
	}

	function startAdd() {
		showAddForm = true;
		reset();

		// Pre-fill client ID and project ID
		$formData.client_id = data.client.client_id;
		$formData.project_id = data.project.project_id;
		$formData.level = 1;
	}

	// TODO: link level and parent item selection

	function cancelAdd() {
		showAddForm = false;
		reset();
	}

	let triggerRef = $state<HTMLButtonElement>(null!);
	function closeAndFocusTrigger() {
		openParentCombobox = false;
		tick().then(() => {
			triggerRef?.focus();
		});
	}
</script>

<div class="container max-w-(--breakpoint-xl) py-8">
	<div class="mb-4 flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold">{data.project.name} - Work Breakdown Structure</h1>
			<p class="mt-1 text-slate-600">
				Manage project-specific WBS items and view client-level items
			</p>
		</div>
	</div>

	<div class="mb-6 flex justify-end">
		{#if !showAddForm}
			<Button onclick={startAdd}>Add Project WBS Item</Button>
		{/if}
	</div>

	{#if showAddForm}
		<div class="mb-8 rounded-lg border bg-white p-6">
			<h2 class="mb-4 text-xl font-semibold">Add New Project WBS Item</h2>

			<form method="POST" action="?/createItem" class="space-y-6" use:superEnhance>
				<input type="hidden" name="client_id" value={$formData.client_id} />
				<input type="hidden" name="project_id" value={$formData.project_id} />

				<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
					<Form.Field {form} name="level">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Level</Form.Label>
								<Input {...props} type="number" bind:value={$formData.level} min="1" />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="parent_item_id">
						<Popover.Root bind:open={openParentCombobox}>
							<Form.Control id={triggerId}>
								{#snippet children({ props })}
									<Form.Label>Parent Item</Form.Label>
									<Popover.Trigger
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-between',
											!$formData.parent_item_id && 'text-muted-foreground',
										)}
										role="combobox"
										bind:ref={triggerRef}
										{...props}
									>
										{$formData.parent_item_id
											? getParentName($formData.parent_item_id)
											: 'Select parent item'}
										<CaretUpDownIcon class="ml-2 size-4 shrink-0 opacity-50" />
									</Popover.Trigger>
									<input hidden value={$formData.parent_item_id} name={props.name} />
								{/snippet}
							</Form.Control>
							<Popover.Content class="w-[400px] p-0">
								<Command.Root>
									<Command.Input autofocus placeholder="Search parent items..." class="h-9" />
									<Command.Empty>No matching items found.</Command.Empty>
									<Command.Group class="max-h-[300px] overflow-y-auto">
										{#each parentOptions as option (option.value)}
											<Command.Item
												value={option.label}
												onSelect={() => {
													$formData.parent_item_id = option.value;
													closeAndFocusTrigger();
												}}
											>
												<span
													class={option.source === 'client'
														? 'text-blue-600'
														: option.source === 'main'
															? 'text-green-600'
															: ''}
												>
													{option.label}
												</span>
												<CheckIcon
													class={cn(
														'ml-auto size-4',
														option.value !== String($formData.parent_item_id) && 'text-transparent',
													)}
												/>
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
						<Form.Description>
							{#if $formData.level > 1}
								Showing only level {$formData.level - 1} items as possible parents. Client items in blue,
								library items in green.
							{:else}
								{data.wbsLibrary?.name || 'Main library'} items in green and client-wide items are shown
								in blue.
							{/if}
						</Form.Description>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<Form.Field {form} name="code">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Code</Form.Label>
							<Input {...props} value={$formData.code ?? ''} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="in_level_code">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>In Level Code</Form.Label>
							<Input {...props} value={$formData.in_level_code ?? ''} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="description">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Description</Form.Label>
							<Input {...props} value={$formData.description ?? ''} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="cost_scope">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Scope Details</Form.Label>
							<Textarea {...props} value={$formData.cost_scope ?? ''} rows={4} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<div class="flex justify-end space-x-2 pt-4">
					<Button type="button" variant="outline" onclick={cancelAdd}>Cancel</Button>
					<Form.Button>Save WBS Item</Form.Button>
				</div>
			</form>
		</div>
	{/if}

	<!-- Client-wide WBS Items -->
	{#if data.clientItems.length > 0}
		<div class="mb-8 rounded-lg border bg-white p-6">
			<h2 class="mb-4 text-xl font-semibold">Client-wide WBS Items</h2>
			<p class="mb-4 text-sm text-slate-600">
				These items are shared across all projects for this client
			</p>

			<div class="overflow-x-auto">
				<table class="min-w-full divide-y divide-gray-200">
					<thead class="bg-gray-50">
						<tr>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Code
							</th>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Description
							</th>
							{#if showClientScopeColumn}
								<th
									scope="col"
									class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
								>
									Scope
								</th>
							{/if}
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Parent
							</th>
						</tr>
					</thead>
					<tbody class="divide-y divide-gray-200 bg-white">
						{#each data.clientItemsTree as item (item.wbs_library_item_id)}
							<WbsTreeItem
								{item}
								depth={0}
								showScope={showClientScopeColumn}
								expandedCategories={expandedClientCategories}
							/>
						{/each}
					</tbody>
				</table>
			</div>
		</div>
	{/if}

	<!-- Project-specific WBS Items -->
	<div class="mb-6 rounded-lg border bg-white p-6">
		<h2 class="mb-4 text-xl font-semibold">Project-specific WBS Items</h2>

		{#if data.projectItems.length === 0}
			<div class="rounded-lg border bg-slate-50 p-8 text-center">
				<p class="text-slate-600">No project-specific WBS items yet. Add some items above.</p>
			</div>
		{:else}
			<div class="overflow-x-auto">
				<table class="min-w-full divide-y divide-gray-200">
					<thead class="bg-gray-50">
						<tr>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Code
							</th>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Description
							</th>
							{#if showProjectScopeColumn}
								<th
									scope="col"
									class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
								>
									Scope
								</th>
							{/if}

							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Actions
							</th>
						</tr>
					</thead>
					<tbody class="divide-y divide-gray-200 bg-white">
						{#each data.projectItemsTree as item (item.wbs_library_item_id)}
							<WbsTreeItem
								{item}
								depth={0}
								showScope={showProjectScopeColumn}
								expandedCategories={expandedProjectCategories}
							>
								{#snippet additionalColumns()}
									<td class="px-6 py-4 text-sm whitespace-nowrap">
										<div class="flex space-x-2">
											<form method="POST" action="?/updateItemShare" use:superEnhance>
												<input
													type="hidden"
													name="wbs_library_item_id"
													value={item.wbs_library_item_id}
												/>
												<input type="hidden" name="makeClientWide" value="true" />
												<input type="hidden" name="project_id" value={data.project.project_id} />
												<Button
													type="submit"
													variant="outline"
													size="sm"
													class="text-blue-600 hover:bg-blue-50"
												>
													Share with Client
												</Button>
											</form>

											<a
												href="/org/{page.params.org_name}/clients/{data.client.name}/projects/{data
													.project.name}/wbs/{item.wbs_library_item_id}"
											>
												<Button variant="outline" size="sm">Edit</Button>
											</a>

											<form method="POST" action="?/deleteItem" use:superEnhance>
												<input
													type="hidden"
													name="wbs_library_item_id"
													value={item.wbs_library_item_id}
												/>
												<Button
													type="submit"
													variant="outline"
													size="sm"
													class="text-red-600 hover:bg-red-50"
												>
													Delete
												</Button>
											</form>
										</div>
									</td>
								{/snippet}
							</WbsTreeItem>
						{/each}
					</tbody>
				</table>
			</div>
		{/if}
	</div>

	<!-- Primary WBS Library Items -->
	<div class="rounded-lg border bg-white p-6">
		<h2 class="mb-4 text-xl font-semibold">{data.wbsLibrary.name} Library Items</h2>

		{#if data.mainWbsLibraryItems.length === 0}
			<div class="rounded-lg border bg-slate-50 p-8 text-center">
				<p class="text-slate-600">No primary WBS library items yet. Add some items above.</p>
			</div>
		{:else}
			<div class="overflow-x-auto">
				<table class="min-w-full divide-y divide-gray-200">
					<thead class="bg-gray-50">
						<tr>
							<th
								scope="col"
								class="w-40 px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Code
							</th>
							<th
								scope="col"
								class="w-40 px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Description
							</th>
							{#if showMainScopeColumn}
								<th
									scope="col"
									class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
								>
									Scope
								</th>
							{/if}
						</tr>
					</thead>
					<tbody class="divide-y divide-gray-200 bg-white">
						{#each data.mainItemsTree as item (item.wbs_library_item_id)}
							<WbsTreeItem
								{item}
								depth={0}
								showScope={showMainScopeColumn}
								bind:expandedCategories={expandedMainCategories}
							></WbsTreeItem>
						{/each}
					</tbody>
				</table>
			</div>
		{/if}
	</div>
</div>
