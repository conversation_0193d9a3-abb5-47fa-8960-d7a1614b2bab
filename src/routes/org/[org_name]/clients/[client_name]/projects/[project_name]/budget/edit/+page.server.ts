import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { fail } from '@sveltejs/kit';
import { getBudgetLineItems, upsertBudgetLineItem } from '$lib/project_utils';
import type { PageServerLoad } from './$types';
import { budgetItemSchema } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ params, locals, url, cookies }) => {
	await requireUser(cookies);

	const { supabase } = locals;
	const budget_line_item_id = url.searchParams.get('id');
	const { org_name, client_name, project_name } = requireProject(params, cookies);

	if (!budget_line_item_id) {
		return redirect(
			`/org/${params.org_name}/clients/${client_name}/projects/${project_name}/budget`,
			{ type: 'error', message: 'Budget item not found' },
			cookies,
		);
	}

	// Fetch the project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name, client_id)')
		.eq('organization.name', params.org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		return redirect(
			`/org/${org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Check user permissions
	const { data: canEditProject } = await supabase.rpc('can_modify_project', {
		project_id_param: projectData.project_id,
	});

	if (!canEditProject) {
		return redirect(
			`/org/${org_name}/clients/${client_name}/projects/${project_name}/budget`,
			{ type: 'error', message: 'You do not have permission to edit this project' },
			cookies,
		);
	}

	// Get budget items
	let budgetItems: Awaited<ReturnType<typeof getBudgetLineItems>> = [];
	try {
		budgetItems = await getBudgetLineItems(supabase, projectData.project_id);
	} catch (error) {
		console.error('Error fetching budget items:', error);
	}

	// Find the specific budget item
	const budgetItem = budgetItems.find(
		(item) => item.budget_line_item_id === parseInt(budget_line_item_id),
	);

	if (!budgetItem) {
		return redirect(
			`/org/${org_name}/clients/${client_name}/projects/${project_name}/budget`,
			{ type: 'error', message: 'Budget item not found' },
			cookies,
		);
	}

	// Get the WBS item for this budget item
	const { data: wbsItem } = await supabase
		.from('wbs_library_item')
		.select('wbs_library_item_id, code, description')
		.eq('wbs_library_item_id', budgetItem.wbs_library_item_id)
		.limit(1)
		.maybeSingle();

	if (!wbsItem) {
		return redirect(
			`/org/${org_name}/clients/${client_name}/projects/${project_name}/budget`,
			{ type: 'error', message: 'WBS item not found' },
			cookies,
		);
	}

	// Create the form with the budget item schema and prefill it
	const form = await superValidate(budgetItem, zod(budgetItemSchema));

	return {
		client: projectData.client,
		project: projectData,
		wbsItem,
		form,
	};
};

export const actions = {
	updateBudgetItem: async ({ request, locals, cookies }) => {
		const { supabase } = locals;

		// Validate the form
		const form = await superValidate(request, zod(budgetItemSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Check permissions
		const { data: canEdit } = await supabase.rpc('can_modify_project', {
			project_id_param: form.data.project_id,
		});

		if (!canEdit) {
			return message(
				form,
				{ type: 'error', text: 'You do not have permission to edit this project' },
				{ status: 403 },
			);
		}

		// Update the budget line item
		try {
			// Use the upsertBudgetLineItem utility to ensure proper cost calculation
			await upsertBudgetLineItem(supabase, form.data, 'Updated from edit page');
		} catch (error) {
			console.error('Error updating budget line item:', error);
			return message(
				form,
				{ type: 'error', text: 'Error saving budget line item' },
				{ status: 500 },
			);
		}
		redirect(
			`../budget`,
			{ type: 'success', message: 'Budget line item updated successfully' },
			cookies,
		);
	},
};
