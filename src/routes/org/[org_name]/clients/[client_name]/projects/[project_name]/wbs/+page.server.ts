import { error, fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { wbsLibraryItemSchema, wbsLibraryItemWithIdSchema } from '$lib/schemas/wbs';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { buildWbsItemTree } from '$lib/wbs_utils';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals, params, cookies }) => {
	// Check authentication
	await requireUser(cookies);

	const { supabase } = locals;
	const { client_name, project_name, org_name } = params;

	// Fetch the project and client data
	const { data: project, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(*)')
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !project || !project.client) {
		console.error('Error fetching project:', projectError);
		return redirect(
			`/org/${org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found.' },
			cookies,
		);
	}

	// Check if user has permission to access this project
	const { data: hasPermission, error: permissionError } = await supabase.rpc('can_access_project', {
		project_id_param: project.project_id,
	});

	if (permissionError) {
		console.error('Error checking project permissions:', permissionError);
		throw error(500, { message: 'Error checking permissions' });
	}

	if (!hasPermission) {
		throw error(403, { message: 'You do not have permission to access this project' });
	}

	// Fetch main project WBS library items
	const { error: libError, data: wbsLibrary } = await supabase
		.from('wbs_library')
		.select('*')
		.eq('wbs_library_id', project.wbs_library_id)
		.limit(1)
		.maybeSingle();

	if (libError || !wbsLibrary) {
		console.error('Error fetching WBS library:', libError);
		throw error(500, { message: 'Error loading WBS library' });
	}

	const { error: mainWbsLibraryItemsError, data: mainWbsLibraryItems } = await supabase
		.from('wbs_library_item')
		.select('*')
		.eq('wbs_library_id', project.wbs_library_id)
		.eq('item_type', 'Standard')
		.order('code');

	if (mainWbsLibraryItemsError) {
		console.error('Error fetching main WBS library items:', mainWbsLibraryItemsError);
		throw error(500, { message: 'Error loading main WBS library items' });
	}

	// Fetch client-wide WBS items (to be inherited by all projects)
	const { data: clientItems, error: clientItemsError } = await supabase
		.from('wbs_library_item')
		.select('*')
		.eq('client_id', project.client.client_id)
		.eq('item_type', 'Custom')
		.is('project_id', null)
		.order('code');

	if (clientItemsError) {
		console.error('Error fetching client WBS items:', clientItemsError);
		throw error(500, { message: 'Error loading client WBS items' });
	}

	// Fetch project-specific WBS items
	const { data: projectItems, error: projectItemsError } = await supabase
		.from('wbs_library_item')
		.select('*')
		.eq('client_id', project.client.client_id)
		.eq('item_type', 'Custom')
		.eq('project_id', project.project_id)
		.order('code');

	if (projectItemsError) {
		console.error('Error fetching project WBS items:', projectItemsError);
		throw error(500, { message: 'Error loading project WBS items' });
	}

	// Convert flat items structures to hierarchical trees
	const mainItemsTree = buildWbsItemTree(mainWbsLibraryItems);
	const clientItemsTree = buildWbsItemTree(clientItems);
	const projectItemsTree = buildWbsItemTree(projectItems);

	// Create form with schema validation
	const form = await superValidate({ item_type: 'Custom' as const }, zod(wbsLibraryItemSchema));

	return {
		client: project.client,
		project,
		wbsLibrary,
		mainWbsLibraryItems,
		mainItemsTree,
		clientItems,
		clientItemsTree,
		projectItems,
		projectItemsTree,
		form,
	};
};

export const actions: Actions = {
	createItem: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;
		const { client_name, project_name, org_name } = params;

		// Validate form data
		const form = await superValidate(request, zod(wbsLibraryItemSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Fetch the client and project
		const { data: project, error: projectError } = await supabase
			.from('project')
			.select('*, client!inner(*)')
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (projectError || !project || !project.client) {
			console.error('Error fetching project:', projectError);
			return redirect(
				`/org/${org_name}/clients/${client_name}`,
				{ type: 'error', message: 'Project not found.' },
				cookies,
			);
		}

		// Check if user has permission to access this project
		const { data: hasPermission, error: permissionError } = await supabase.rpc(
			'can_modify_project',
			{
				project_id_param: project.project_id,
			},
		);

		if (permissionError) {
			console.error('Error checking project permissions:', permissionError);
			throw error(500, { message: 'Error checking permissions' });
		}

		if (!hasPermission) {
			throw error(403, { message: 'You do not have permission to access this project' });
		}

		// Ensure client_id matches the current client
		if (form.data.client_id !== project.client.client_id) {
			return fail(400, { form, error: 'Invalid client ID' });
		}

		// Ensure project_id matches the current project
		if (form.data.project_id !== project.project_id) {
			return fail(400, { form, error: 'Invalid project ID' });
		}

		// Confirm that the code starts with the parent_id's code
		if (form.data.parent_item_id) {
			const { data: parentItem, error: parentError } = await supabase
				.from('wbs_library_item')
				.select('code')
				.eq('wbs_library_item_id', form.data.parent_item_id)
				.limit(1)
				.maybeSingle();

			if (parentError || !parentItem) {
				console.error('Error fetching parent item:', parentError);
				return fail(400, { form, error: 'Parent item not found' });
			}

			if (!form.data.code.startsWith(parentItem.code)) {
				return fail(400, { form, error: "Code must start with the parent item's code" });
			}
		}

		// Insert the new WBS item
		const { error: insertError } = await supabase.from('wbs_library_item').insert({
			wbs_library_id: project.wbs_library_id,
			code: form.data.code,
			description: form.data.description,
			cost_scope: form.data.cost_scope,
			parent_item_id: form.data.parent_item_id,
			level: form.data.level,
			in_level_code: form.data.in_level_code,
			item_type: 'Custom',
			client_id: form.data.client_id,
			project_id: form.data.project_id,
		});

		if (insertError) {
			console.error('Error creating WBS item:', insertError);
			return fail(500, { form, error: 'Failed to create WBS item' });
		}

		return message(form, { type: 'success', text: 'WBS item created successfully' });
	},

	deleteItem: async ({ request, locals }) => {
		const { supabase } = locals;
		// Validate form data
		const form = await superValidate(request, zod(wbsLibraryItemWithIdSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const id = form.data.wbs_library_item_id;
		if (!id) {
			return fail(400, { error: 'Item ID is required' });
		}

		// Delete the WBS item
		const { error: deleteError } = await supabase
			.from('wbs_library_item')
			.delete()
			.eq('wbs_library_item_id', id)
			.eq('item_type', 'Custom');

		if (deleteError) {
			console.error('Error deleting WBS item:', deleteError);
			return fail(500, { error: 'Failed to delete WBS item' });
		}

		return message(form, { type: 'success', text: 'WBS item deleted successfully' });
	},

	updateItemShare: async ({ request, locals }) => {
		const { supabase } = locals;
		const form = await superValidate(request, zod(wbsLibraryItemWithIdSchema));
		if (!form.valid) {
			return fail(400, { form });
		}
		const id = form.data.wbs_library_item_id;
		const makeClientWide = !form.data.project_id;

		if (!id) {
			return fail(400, { error: 'Item ID is required' });
		}

		// Update the project_id field based on sharing preference
		const { error: updateError } = await supabase
			.from('wbs_library_item')
			.update({
				project_id: makeClientWide ? null : form.data.project_id,
			})
			.eq('wbs_library_item_id', id)
			.eq('item_type', 'Custom');

		if (updateError) {
			console.error('Error updating WBS item:', updateError);
			return fail(500, { error: 'Failed to update WBS item sharing' });
		}

		return message(form, {
			type: 'success',
			text: makeClientWide
				? 'WBS item is now shared with all client projects'
				: 'WBS item is now specific to this project only',
		});
	},
};
