<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import type { PageData } from './$types';
	import { invalidate } from '$app/navigation';
	import * as Dialog from '$lib/components/ui/dialog';
	import { page } from '$app/state';

	let { data }: { data: PageData } = $props();
	const projectMembers = $derived(data.projectMembers);
	const names = $derived(data.names);
	const invites = $derived(data.invites);
	const project = $derived(data.project);

	async function removeUser(permissionId: number) {
		const formData = new FormData();
		formData.append('permissionId', permissionId.toString());

		await fetch(`?/remove`, {
			method: 'POST',
			body: formData,
		});

		invalidate('project:team');
	}
</script>

<div class="container mx-auto py-8">
	<div class="@container flex flex-col gap-6">
		<div class="flex w-full items-start justify-between">
			<h1>Team Members</h1>
			{#if data.canEditProject}
				<Button
					href={`/org/${page.params.org_name}/clients/${project.client.name}/projects/${project.name}/invite`}
					>Invite</Button
				>
			{/if}
		</div>
		<!-- Team Members Section -->
		<div class="rounded-lg border">
			{#if (projectMembers?.length || 0) + (invites?.length || 0) === 0}
				<div class="p-6 pt-0 text-center">
					<p class="text-muted-foreground">No team members yet.</p>
				</div>
			{:else}
				<div class="overflow-x-auto">
					<table class="w-full">
						<thead>
							<tr class="bg-muted/50 border-b">
								<th class="px-6 py-3 text-left font-medium">User</th>
								<th class="px-6 py-3 text-left font-medium">Role</th>
								<th class="px-6 py-3 text-left font-medium">Status</th>
								<th class="py-3 pr-9 pl-6 text-right font-medium">Actions</th>
							</tr>
						</thead>
						<tbody>
							{#each projectMembers as member (member)}
								<tr class="hover:bg-muted/50 border-b last:border-b-0">
									<td class="px-6 py-3">
										<div>
											<span class="block font-medium">
												{member?.user_id ? names?.[member?.user_id] : 'Unnamed User'}
											</span>
											<span class="text-muted-foreground block text-sm">
												{member.email}
											</span>
										</div>
									</td>
									<td class="px-6 py-3">
										<span class="capitalize">{member.role ?? `${member.access_via} member`}</span>
									</td>
									<td class="px-6 py-3">
										<span
											class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800"
										>
											Active
										</span>
									</td>
									{#if data.isProjectAdmin && member?.user_id !== data.user?.id && member.membership_id}
										{#if member.access_via === 'project'}
											<td class="px-6 py-3 text-right">
												<Dialog.Root>
													<Dialog.Trigger>
														<Button variant="ghost" size="sm">Remove</Button>
													</Dialog.Trigger>
													<Dialog.Content class="sm:max-w-[425px]">
														<Dialog.Header>
															<Dialog.Title>Remove Team Member</Dialog.Title>
															<Dialog.Description>
																Are you sure you want to remove this team member from this project?
															</Dialog.Description>
														</Dialog.Header>
														<div class="py-4">
															<p class="text-muted-foreground text-sm">
																This action will revoke {names[member?.user_id] ||
																	member.email.split('@')[0]}'s access to this project.
															</p>
														</div>
														<Dialog.Footer>
															<Dialog.Close>
																<Button variant="outline" class="mr-2">Cancel</Button>
															</Dialog.Close>
															<Button
																variant="destructive"
																onclick={() => removeUser(member.membership_id!)}
															>
																Remove Member
															</Button>
														</Dialog.Footer>
													</Dialog.Content>
												</Dialog.Root>
											</td>
										{/if}
									{/if}
								</tr>
							{/each}
							{#each invites as invite (invite.invite_id)}
								<tr class="hover:bg-muted/50 border-b last:border-b-0">
									<td class="px-6 py-3">
										<div>
											<span class="block font-medium">
												{invite.invitee_email}
											</span>
											<span class="text-muted-foreground block text-sm"> Pending invitation </span>
										</div>
									</td>
									<td class="px-6 py-3">
										<span class="capitalize">{invite.role}</span>
									</td>
									<td class="px-6 py-3">
										<span
											class="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800"
										>
											Pending
										</span>
									</td>
									<!-- TODO: implement disinvitation -->
									<!-- <td class="px-6 py-3 text-right">
										<Button
											variant="ghost"
											size="sm"
                                                                               onclick={() => disinviteUser(invite.membership_id)}
										>
											Disinvite
										</Button>
								</td> -->
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			{/if}
		</div>
	</div>
</div>
