import { z } from 'zod';

/**
 * One budget line item extracted from the CostX sheet
 * and ready for insertion.
 */
export const ImportLineItemSchema = z.object({
	// Foreign-key lookup for wbs_library_item; we
	// pass the CostX code string so the backend can
	// create/lookup the WBS item in one transaction.
	code: z.string().min(1),

	// Full description after we've inserted any
	// [Category] prefixes.
	description: z.string().min(1),

	quantity: z.number().nonnegative(),
	unit: z.string().min(1),

	// "Rate" in the CostX file maps to material_rate.
	material_rate: z.number().nonnegative(),

	// Optional cells
	factor: z.number().nonnegative().optional(),

	// You can include optional fields we might expose in
	// the UI later (labor_rate, productivity_per_hour, remarks…)
	labor_rate: z.number().nonnegative().optional(),
	productivity_per_hour: z.number().nonnegative().optional(),
	remarks: z.string().optional(),
});

/**
 * Overall payload sent to the RPC.
 */
export const ImportDataSchema = z.object({
	// Context for the server-side insert
	project_id: z.string().uuid(),

	// Rows the user has confirmed for import
	items: z.array(ImportLineItemSchema).min(1, 'At least one line item must be provided'),
});
