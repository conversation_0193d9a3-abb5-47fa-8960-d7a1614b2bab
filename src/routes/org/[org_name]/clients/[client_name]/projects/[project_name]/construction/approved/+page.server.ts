import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { type Actions, fail } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { approvedChangesFilterSchema, type approvedChangeStatuses } from '$lib/schemas/risk';

export const load: PageServerLoad = async ({ params, locals, cookies, url, depends }) => {
	depends('project:approved-changes');

	await requireUser(cookies);

	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject(params, cookies);

	// Fetch the project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name, client_id, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		return redirect(
			`/org/${params.org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Check user permissions
	const { data: canEditProject } = await supabase.rpc('can_modify_project', {
		project_id_param: projectData.project_id,
	});

	// Get filter parameters from URL
	const statusFilter = (url.searchParams.get('status') || 'all') as
		| (typeof approvedChangeStatuses)[number]
		| 'all';
	const dateFromFilter = url.searchParams.get('date_from') || undefined;
	const dateToFilter = url.searchParams.get('date_to') || undefined;
	const wbsItemFilter = url.searchParams.get('wbs_item') || undefined;

	// Fetch WBS items for the project
	const { data: wbsItems } = await supabase
		.from('wbs_library_item')
		.select('wbs_library_item_id, code, description')
		.eq('project_id', projectData.project_id)
		.order('code', { ascending: true });

	// Build query for approved changes
	let approvedChangesQuery = supabase
		.from('approved_changes')
		.select(
			`
			*,
			wbs_item:wbs_library_item(wbs_library_item_id, code, description),
			risk_owner:profile!approved_changes_risk_owner_user_id_fkey(full_name, email),
			approved_by:profile!approved_changes_approved_by_user_id_fkey(full_name, email)
		`,
		)
		.eq('project_id', projectData.project_id)
		.order('date_approved', { ascending: false });

	// Apply filters
	if (statusFilter && statusFilter !== 'all') {
		approvedChangesQuery = approvedChangesQuery.eq('status', statusFilter);
	}

	if (dateFromFilter) {
		approvedChangesQuery = approvedChangesQuery.gte('date_approved', dateFromFilter);
	}

	if (dateToFilter) {
		approvedChangesQuery = approvedChangesQuery.lte('date_approved', dateToFilter);
	}

	if (wbsItemFilter) {
		approvedChangesQuery = approvedChangesQuery.eq('wbs_library_item_id', wbsItemFilter);
	}

	// Execute the query
	const { data: approvedChanges, error: approvedChangesError } = await approvedChangesQuery;

	console.log({ approvedChanges });

	if (approvedChangesError) {
		console.error('Error fetching approved changes:', approvedChangesError);
	}

	// Fetch budget data for WBS items to show original budget amounts
	const { data: budgetData } = await supabase
		.from('budget_line_item_current')
		.select('wbs_library_item_id, quantity, unit_rate')
		.eq('project_id', projectData.project_id);

	// Create a map of WBS item to total budget amount
	const budgetMap = new Map<string, number>();
	if (budgetData) {
		budgetData.forEach((item) => {
			const currentTotal = budgetMap.get(item.wbs_library_item_id) || 0;
			budgetMap.set(item.wbs_library_item_id, currentTotal + item.quantity * item.unit_rate);
		});
	}

	// Create the filter form
	const filterForm = await superValidate(
		{
			status: statusFilter,
			date_from: dateFromFilter,
			date_to: dateToFilter,
			wbs_library_item_id: wbsItemFilter,
		},
		zod(approvedChangesFilterSchema),
	);

	return {
		project: projectData,
		client: projectData.client,
		approvedChanges: approvedChanges || [],
		wbsItems: wbsItems || [],
		budgetMap: Object.fromEntries(budgetMap),
		canEditProject: !!canEditProject,
		filterForm,
		filters: {
			status: statusFilter,
			date_from: dateFromFilter,
			date_to: dateToFilter,
			wbs_item: wbsItemFilter,
		},
	};
};

export const actions: Actions = {
	// Apply filters
	applyFilters: async ({ request }) => {
		const form = await superValidate(request, zod(approvedChangesFilterSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Return the form data to be used for URL parameters
		return { filterForm: form };
	},
};
