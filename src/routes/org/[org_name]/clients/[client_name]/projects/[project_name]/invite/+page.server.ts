import { fail } from '@sveltejs/kit';
import { zod } from 'sveltekit-superforms/adapters';
import { message, superValidate } from 'sveltekit-superforms/server';
import type { PageServerLoad, Actions } from './$types';
import { projectInviteSchema } from '$lib/schemas/project';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals, parent, cookies, depends }) => {
	depends('project:invite');

	const { supabase } = locals;

	const { project } = await parent();

	const { data, error } = await supabase.rpc('is_project_owner', {
		project_id_param: project.project_id,
	});
	if (error) {
		console.error('Error checking project owner:', error);
		return redirect(403, '/');
	}
	if (!data) {
		console.error('Insufficient permissions', data);
		return redirect('/', { type: 'error', message: 'Insufficient permissions' }, cookies);
	}
	const form = await superValidate(zod(projectInviteSchema));

	return { form };
};

export const actions: Actions = {
	default: async ({ request, locals, params, cookies, fetch }) => {
		const form = await superValidate(request, zod(projectInviteSchema));
		if (!form.valid) return fail(400, { form });

		await requireUser(cookies);
		const { supabase } = locals;
		const { project_name } = params;
		const { data: project, error: projectError } = await supabase
			.from('project')
			.select('project_id')
			// TODO: add client name and org
			.eq('name', project_name)
			.limit(1)
			.single();

		if (projectError || !project) {
			console.error('Error fetching project:', projectError);
			return fail(404, { form, error: 'Project not found' });
		}
		const { data: isOwner, error } = await supabase.rpc('is_project_owner', {
			project_id_param: project.project_id,
		});
		if (error) {
			console.error('Error checking project owner:', error);
			return redirect(403, '/');
		}
		if (!isOwner) {
			console.error('Insufficient permissions', isOwner);
			return redirect('/', { type: 'error', message: 'Insufficient permissions' }, cookies);
		}

		// Lookup profile by email
		const { data: userProfile, error: findError } = await supabase
			.from('profile')
			.select('user_id')
			.eq('email', form.data.email)
			.maybeSingle();
		if (findError)
			return message(form, {
				text: 'Something went wrong looking up that email address. Please try again.',
				type: 'error',
			});

		if (!userProfile) {
			// User not found, send invitation
			const res = await fetch('/api/invites', {
				method: 'POST',
				body: JSON.stringify({
					resourceType: 'project',
					resourceId: project.project_id,
					role: form.data.role,
					inviteeEmail: form.data.email,
					expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
				}),
			});
			if (!res.ok)
				return message(form, {
					text: 'Something went wrong sending that invitation. Please try again.',
					type: 'error',
				});
			return message(form, { text: 'Invitation sent', type: 'success' });
		}

		// User found, add to membership table
		// TODO: don't auto add users - send an invitation instead, requires a new page or inbox for pending invites
		const { error: memberError } = await supabase.from('membership').insert({
			entity_id: project.project_id,
			entity_type: 'project',
			user_id: userProfile.user_id,
			role: form.data.role,
		});
		if (memberError) {
			form.errors.email = [memberError.message];
			return message(form, { text: 'Something went wrong', type: 'error' });
		}

		return message(form, { text: 'User added to project', type: 'success' });
	},
};
