import { projectSchema } from '$lib/schemas/project';
import { fail } from '@sveltejs/kit';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import type { Actions, PageServerLoad } from './$types';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals, params, cookies }) => {
	await requireUser(cookies);

	const { supabase } = locals;
	const { project_name, client_name, org_name } = params;

	// Get organization by name
	const { data: organization, error: orgError } = await supabase
		.rpc('get_organization_by_name', { org_name_param: org_name })
		.maybeSingle();

	if (orgError || !organization) {
		return redirect('/', { type: 'error', message: 'Organization not found.' }, cookies);
	}

	// Fetch client id from client name
	const { data: project, error } = await supabase
		.from('project')
		.select('*, client!inner(name)')
		.eq('client.org_id', organization.org_id)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (error || !project) {
		console.error('Error fetching project:', error);
		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found.' },
			cookies,
		);
	}

	// Fetch available WBS libraries for the dropdown
	const { data: wbsLibraries } = await supabase
		.from('wbs_library')
		.select('wbs_library_id, name')
		.order('name');

	// Create a form using the project schema and pre-populate with project data
	const form = await superValidate(project, zod(projectSchema));

	return {
		form,
		project,
		wbsLibraries: wbsLibraries || [],
	};
};

export const actions: Actions = {
	default: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;
		const { client_name, project_name, org_name } = params;

		// Validate form data
		const form = await superValidate(request, zod(projectSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Get organization by name
		const { data: organization, error: orgError } = await supabase
			.rpc('get_organization_by_name', { org_name_param: org_name })
			.maybeSingle();

		if (orgError || !organization) {
			return redirect('/', { type: 'error', message: 'Organization not found.' }, cookies);
		}

		// Fetch project id from project name and client name
		const { data: projectData, error: projectError } = await supabase
			.from('project')
			.select('project_id, client!inner(name)')
			.eq('client.org_id', organization.org_id)
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (projectError || !projectData) {
			console.error('Error fetching project ID:', projectError);
			return message(form, { type: 'error', text: 'Project not found.' }, { status: 404 });
		}

		// Check if user has permission to edit this project
		const { data: hasPermission, error: permissionError } = await supabase.rpc(
			'can_modify_project',
			{
				project_id_param: projectData.project_id,
			},
		);

		if (permissionError || !hasPermission) {
			console.error('Permission error:', permissionError);
			return message(
				form,
				{ type: 'error', text: 'You do not have permission to edit this project.' },
				{ status: 403 },
			);
		}

		// Update the project
		const { error: updateError } = await supabase
			.from('project')
			.update(form.data)
			.eq('project_id', projectData.project_id);

		if (updateError) {
			console.error('Error updating project:', updateError);
			return message(form, { type: 'error', text: 'Error updating project.' }, { status: 400 });
		}

		return message(form, { type: 'success', text: 'Project updated successfully.' });
	},
};
