import { clientSchema } from '$lib/schemas/client';
import { fail } from '@sveltejs/kit';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import type { Actions, PageServerLoad } from './$types';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals, cookies }) => {
	await requireUser(cookies);

	if (!locals.orgId) {
		return redirect(
			'/org/new',
			{
				type: 'error',
				message:
					'You must be a member of an organization to view this page. Create one or choose your organization from the sidebar.',
			},
			cookies,
		);
	}
	// Create a form using the client schema
	const form = await superValidate(zod(clientSchema));

	return {
		form,
	};
};

export const actions: Actions = {
	default: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;
		const formData = await request.formData();

		// Validate form data
		const form = await superValidate(formData, zod(clientSchema));

		if (!form.valid) {
			return fail(400, { form });
		}
		const { user } = await requireUser(cookies);

		const { orgId } = locals;

		if (!orgId) {
			return message(
				form,
				{ type: 'error', text: 'You must be a member of an organization to create a client.' },
				{
					status: 400,
				},
			);
		}

		// Note: Logo handling is removed for now as there's no logo field in the clients table

		// Insert the client
		const { error: insertError } = await supabase.from('client').insert({
			name: form.data.name,
			description: form.data.description,
			client_url: form.data.client_url,
			internal_url: form.data.internal_url,
			internal_url_description: form.data.internal_url_description,
			org_id: orgId,
			created_by_user_id: user.id,
		});

		if (insertError) {
			console.error('Error creating client:', insertError);
			return message(form, { type: 'error', text: 'Error creating client.' }, { status: 400 });
		}

		return redirect(
			`/org/${encodeURIComponent(params.org_name)}/clients/${encodeURIComponent(form.data.name)}`,
			{ type: 'success', message: 'Client created successfully.' },
			cookies,
		);
	},
};
