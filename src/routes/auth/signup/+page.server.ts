import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { signUpSchema } from '$lib/schemas/auth';

export const load: PageServerLoad = async () => {
	const form = await superValidate(zod(signUpSchema));
	return { form };
};

export const actions: Actions = {
	default: async ({ request, locals }) => {
		const form = await superValidate(request, zod(signUpSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const { error } = await locals.supabase.auth.signUp({
			email: form.data.email,
			password: form.data.password,
		});

		if (error) {
			console.error({ error, email: form.data.email });
			// return fail(400, { form, message: error.message });
			return message(form, { type: 'error', text: error.message });
		}

		return message(form, { type: 'success', text: 'User created successfully.' });
	},
};
