import crypto from 'crypto';
import { redirect } from 'sveltekit-flash-message/server';
import { error } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { supabaseServiceClient } from '$lib/server/supabase_client';
import type { PostgrestError } from '@supabase/supabase-js';
import type { Tables } from '$lib/database.types';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { tokenSchema } from '$lib/schemas/invite-request';
import { requireUser } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals, params, cookies }) => {
	const { token } = params;

	if (!token) {
		return redirect(404, '/');
	}

	// if user not logged in, redirect to sign-in page with returnTo query param
	if (!locals.user) {
		return redirect(
			`/auth/signin?invite=${encodeURIComponent(token)}`,
			{ type: 'success', message: 'Sign in or sign up to accept your invitation.' },
			cookies,
		);
	}

	const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
	const { data: invite, error: fetchError } = await supabaseServiceClient
		.from('invite')
		.select('*, profile!inviter_id(full_name)')
		.eq('token_hash', tokenHash)
		.eq('status', 'pending')
		.gte('expires_at', new Date().toISOString())
		.single();
	// TODO: differentiate between expired, revoked, already accepted and not found

	if (fetchError || !invite) throw error(404, 'Invite not found. It may have expired.');

	let resourceData: Tables<'organization'> | Tables<'client'> | Tables<'project'> | null = null;
	let resourceError: PostgrestError | null = null;
	switch (invite.resource_type) {
		case 'organization': {
			({ data: resourceData, error: resourceError } = await supabaseServiceClient
				.from('organization')
				.select('name')
				.eq('org_id', invite.resource_id)
				.single());
			break;
		}
		case 'client': {
			({ data: resourceData, error: resourceError } = await supabaseServiceClient
				.from('client')
				.select('name')
				.eq('client_id', invite.resource_id)
				.single());
			break;
		}
		case 'project': {
			({ data: resourceData, error: resourceError } = await supabaseServiceClient
				.from('project')
				.select('name')
				.eq('project_id', invite.resource_id)
				.single());
			break;
		}
		default: {
			throw error(400, 'Invalid resource type');
		}
	}

	console.log({ resourceData, resourceError });
	if (resourceError || !resourceData) throw error(404, 'Resource not found');

	// if user logged in, present an accept/decline form

	const acceptForm = await superValidate(zod(tokenSchema));
	const declineForm = await superValidate(zod(tokenSchema));

	return {
		token,
		name: resourceData.name,
		role: invite.role,
		resource_type: invite.resource_type,
		inviter: invite.profile.full_name,
		acceptForm,
		declineForm,
	};
};

export const actions: Actions = {
	accept: async ({ request, locals, cookies }) => {
		const form = await superValidate(request, zod(tokenSchema));

		if (!form.valid) {
			return message(form, { type: 'error', text: 'Invalid token' });
		}
		const token = form.data.token;

		const { supabase } = locals;
		const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

		// Call the RPC function to accept the invitation
		const { data, error: inviteError } = await supabase.rpc('accept_invite', {
			token_param: tokenHash,
		});

		if (inviteError) {
			if (inviteError?.code === '23505') {
				// This error code indicates that the user is already a member of the organization
				console.log('User is already a member');
				// set invite to "accepted" in the database
				await supabase
					.from('invite')
					.update({
						status: 'accepted',
						updated_by: locals.user?.id ?? null,
					})
					.eq('token_hash', tokenHash)
					.eq('status', 'pending');
				return redirect(
					'/',
					{
						type: 'success',
						message: 'You are already a member.',
					},
					cookies,
				);
			}
			// Handle other errors
			console.error('Error accepting invitation:', inviteError);
			return message(form, {
				type: 'error',
				text: 'Failed to accept invitation. Please try again.',
			});
		}

		if (!data) {
			return redirect(
				'/',
				{ type: 'success', message: 'Invitation accepted successfully.' },
				cookies,
			);
		}

		// Parse the JSON response
		const responseData = data as unknown as { resource_type: string; resource_id: string };
		switch (responseData.resource_type) {
			case 'organization': {
				return redirect(
					`/`,
					{
						type: 'success',
						message: 'organization invitation accepted.',
					},
					cookies,
				);
			}
			case 'client': {
				return redirect(
					`/clients`,
					{
						type: 'success',
						message: 'Client invitation accepted.',
					},
					cookies,
				);
			}
			case 'project': {
				return redirect(
					`/projects`,
					{
						type: 'success',
						message: 'Project invitation accepted.',
					},
					cookies,
				);
			}
			default: {
				// this should never happen
				console.error('Invalid resource type:', { data });
				return message(form, { type: 'error', text: 'Something went wrong. Please try again.' });
			}
		}
	},

	decline: async ({ request, locals, cookies }) => {
		const form = await superValidate(request, zod(tokenSchema));
		if (!form.valid) {
			return { success: false, error: 'Invalid token' };
		}
		const token = form.data.token;

		const { user } = await requireUser(cookies);
		const userId = user.id;

		const { supabase } = locals;

		// mark invitation as declined
		const { error: declineError } = await supabase
			.from('invite')
			.update({ status: 'declined', updated_by: userId ?? null })
			.eq('token_hash', crypto.createHash('sha256').update(token).digest('hex'))
			.eq('status', 'pending');

		if (declineError) {
			console.error('Error declining invitation:', declineError);
			return message(form, { type: 'error', text: 'Something went wrong. Please try again.' });
		}

		if (userId) {
			return redirect(
				`/`,
				{
					type: 'success',
					message: 'Invitation declined.',
				},
				cookies,
			);
		} else {
			return message(form, {
				type: 'success',
				text: 'Invitation declined. Thanks for letting us know.',
			});
		}
	},
};
