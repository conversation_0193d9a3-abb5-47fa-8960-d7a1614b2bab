<script lang="ts">
	import type { PageData } from './$types';
	import { superForm } from 'sveltekit-superforms';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';

	const { data }: { data: PageData } = $props();
	const formHandler = superForm(data.form);
	const { form, enhance } = formHandler;
</script>

<div class="container mx-auto max-w-md py-16">
	<h1 class="mb-6 text-2xl font-semibold">Sign In</h1>
	<div class="rounded-lg border p-6 shadow-xs">
		<form method="POST" use:enhance>
			{#if data.next}
				<input type="hidden" name="next" value={data.next} />
			{/if}
			<div class="space-y-4">
				<Form.Field form={formHandler} name="email">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Email <span class="text-red-500">*</span></Form.Label>
							<Input
								{...props}
								type="email"
								placeholder="<EMAIL>"
								bind:value={$form.email}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={formHandler} name="password">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Password <span class="text-red-500">*</span></Form.Label>
							<Input
								{...props}
								type="password"
								placeholder="••••••••"
								bind:value={$form.password}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<div class="pt-6">
					<Form.Button class="w-full">Sign In</Form.Button>
				</div>
			</div>
			<div class="mt-4 flex justify-between gap-6 text-center">
				<a href="/auth/reset-password" class="text-left text-sm text-blue-600 hover:underline"
					>Forgot your password?</a
				>
				<a href="/auth/signup" class="text-right text-sm text-blue-600 hover:underline"
					>Need an account?</a
				>
			</div>
		</form>
	</div>
</div>
