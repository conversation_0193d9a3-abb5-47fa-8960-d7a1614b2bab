import { z } from 'zod';

export const signUpSchema = z.object({
	email: z.string().email({ message: 'Invalid email address' }),
	password: z.string().min(8, { message: 'Password must be at least 8 characters' }),
});

export const signInSchema = z.object({
	email: z.string().email({ message: 'Invalid email address' }),
	password: z.string().min(8, { message: 'Password must be at least 8 characters' }),
	next: z.string().optional(),
});

export const resetPasswordSchema = z.object({
	email: z.string().email({ message: 'Invalid email address' }),
});

export const changePasswordSchema = z
	.object({
		password: z.string().min(8, { message: 'Password must be at least 8 characters' }),
		confirmPassword: z
			.string()
			.min(8, { message: 'Confirm password must be at least 8 characters' }),
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "Passwords don't match",
		path: ['confirmPassword'],
	});
