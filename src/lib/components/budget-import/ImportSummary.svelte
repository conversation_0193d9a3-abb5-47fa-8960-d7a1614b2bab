<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { AlertTriangle, CheckCircle, DollarSign, Hash, Layers } from '@lucide/svelte';
	import type { ClassifiedRow, ColumnMapping } from '$lib/budget_import_utils';
	import {
		transformToImportData,
		parseWbsCode,
		getRowStringValue,
		getRowNumericValue,
	} from '$lib/budget_import_utils';
	import { formatCurrency } from '$lib/utils';

	interface Props {
		classifiedRows: ClassifiedRow[];
		columnMapping: ColumnMapping;
		projectId: string;
		onImport: (importData: ReturnType<typeof transformToImportData>) => void;
		onBack: () => void;
		isImporting?: boolean;
	}

	let {
		classifiedRows,
		columnMapping,
		projectId,
		onImport,
		onBack,
		isImporting = false,
	}: Props = $props();

	// Filter detail rows for import
	const detailRows = $derived(classifiedRows.filter((row) => row.classification === 'detail'));

	// Helper functions for calculations using column mapping
	function getRate(row: ClassifiedRow): number {
		// Try different rate fields based on mapping
		if (columnMapping.material_rate !== undefined) {
			return getRowNumericValue(row, 'material_rate', columnMapping);
		}
		if (columnMapping.rate !== undefined) {
			return getRowNumericValue(row, 'rate', columnMapping);
		}
		return 0;
	}

	function getFactor(row: ClassifiedRow): number {
		return getRowNumericValue(row, 'factor', columnMapping) || 1;
	}

	function getQuantity(row: ClassifiedRow): number {
		return getRowNumericValue(row, 'quantity', columnMapping) || 0;
	}

	function getSubtotal(row: ClassifiedRow): number {
		const quantity = getQuantity(row);
		const rate = getRate(row);
		return quantity * rate;
	}

	function getTotal(row: ClassifiedRow): number {
		const subtotal = getSubtotal(row);
		const factor = getFactor(row);
		return subtotal * factor;
	}

	// Calculate statistics
	const totalItems = $derived(detailRows.length);
	const totalBudget = $derived(detailRows.reduce((sum, row) => sum + getTotal(row), 0));

	// Get unique WBS levels that will be created
	const wbsLevels = $derived(() => {
		const levels = new Set<string>();
		detailRows.forEach((row) => {
			const code = getRowStringValue(row, 'code', columnMapping);
			if (code) {
				try {
					const parsed = parseWbsCode(code);
					levels.add(`Level ${parsed.level}`);
				} catch (_e) {
					// Skip invalid codes
				}
			}
		});
		return Array.from(levels).sort();
	});

	// Validation warnings
	let warnings = $derived(() => {
		const warnings: string[] = [];

		detailRows.forEach((row, index) => {
			const quantity = getQuantity(row);
			const rate = getRate(row);
			const code = getRowStringValue(row, 'code', columnMapping);
			const description = getRowStringValue(row, 'description', columnMapping);

			// Check for negative values
			if (quantity < 0) {
				warnings.push(`Row ${index + 1}: Negative quantity (${quantity})`);
			}
			if (rate < 0) {
				warnings.push(`Row ${index + 1}: Negative rate (${rate})`);
			}

			// Check for zero values
			if (quantity === 0) {
				warnings.push(`Row ${index + 1}: Zero quantity`);
			}
			if (rate === 0) {
				warnings.push(`Row ${index + 1}: Zero rate`);
			}

			// Check for missing required fields
			if (!code?.trim()) {
				warnings.push(`Row ${index + 1}: Missing WBS code`);
			}
			if (!row.finalDescription?.trim() && !description?.trim()) {
				warnings.push(`Row ${index + 1}: Missing description`);
			}
		});

		return warnings;
	});

	// Check if import should be blocked
	let hasErrors = $derived(warnings().some((w) => w.includes('Missing')));

	// Calculate grand total for all items
	let grandTotal = $derived(detailRows.reduce((sum, row) => sum + getTotal(row), 0));

	function handleImport() {
		const importData = transformToImportData(classifiedRows, columnMapping, projectId);
		onImport(importData);
	}
</script>

<div class="space-y-6">
	<div>
		<h2 class="mb-2 text-xl font-semibold">Step 5: Review Import</h2>
		<p class="text-muted-foreground">
			Review the import summary and resolve any warnings before proceeding.
		</p>
	</div>

	<!-- Statistics Cards -->
	<div class="grid grid-cols-1 gap-4 md:grid-cols-3">
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">Items to Import</CardTitle>
				<Hash class="text-muted-foreground h-4 w-4" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{totalItems}</div>
				<p class="text-muted-foreground text-xs">Budget line items</p>
			</CardContent>
		</Card>

		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">Total Budget</CardTitle>
				<DollarSign class="text-muted-foreground h-4 w-4" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{formatCurrency(totalBudget)}</div>
				<p class="text-muted-foreground text-xs">Sum of quantity × rate × factor</p>
			</CardContent>
		</Card>

		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">WBS Levels</CardTitle>
				<Layers class="text-muted-foreground h-4 w-4" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold">{wbsLevels.length}</div>
				<p class="text-muted-foreground text-xs">
					{wbsLevels().join(', ')}
				</p>
			</CardContent>
		</Card>
	</div>

	<!-- Warnings -->
	{#if warnings.length > 0}
		<Alert variant={hasErrors ? 'destructive' : 'default'}>
			<AlertTriangle class="h-4 w-4" />
			<AlertDescription>
				<div class="mb-2 font-medium">
					{hasErrors ? 'Errors found - import blocked:' : 'Warnings found:'}
				</div>
				<ul class="max-h-32 list-inside list-disc space-y-1 overflow-y-auto">
					{#each warnings().slice(0, 10) as warning (warning)}
						<li class="text-sm">{warning}</li>
					{/each}
					{#if warnings().length > 10}
						<li class="text-sm font-medium">...and {warnings().length - 10} more</li>
					{/if}
				</ul>
			</AlertDescription>
		</Alert>
	{:else}
		<Alert>
			<CheckCircle class="h-4 w-4" />
			<AlertDescription>
				No issues found. Ready to import {totalItems} budget items.
			</AlertDescription>
		</Alert>
	{/if}

	<!-- Budget Items Table -->
	<Card>
		<CardHeader>
			<CardTitle class="text-lg">Budget Items Preview</CardTitle>
		</CardHeader>
		<CardContent class="p-2">
			<div class="max-h-150 overflow-y-auto">
				<Table class="w-full table-fixed">
					<TableHeader class="bg-background sticky top-0">
						<TableRow>
							<TableHead class="w-24">WBS Code</TableHead>
							<TableHead class="w-48">Description</TableHead>
							<TableHead class="w-20 text-right">Quantity</TableHead>
							<TableHead class="w-16">Unit</TableHead>
							<TableHead class="w-24 text-right">Rate</TableHead>
							<TableHead class="w-24 text-right">Subtotal</TableHead>
							<TableHead class="w-20 text-right">Factor</TableHead>
							<TableHead class="w-24 text-right">Total</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{#each detailRows as row, index (index)}
							<TableRow>
								<TableCell class="font-mono text-xs"
									>{getRowStringValue(row, 'code', columnMapping)}</TableCell
								>
								<TableCell class="w-48 overflow-hidden text-sm overflow-ellipsis"
									>{row.finalDescription ||
										getRowStringValue(row, 'description', columnMapping)}</TableCell
								>
								<TableCell class="text-right text-sm tabular-nums">
									{getQuantity(row).toLocaleString(undefined, {
										minimumFractionDigits: 0,
										maximumFractionDigits: 2,
									})}
								</TableCell>
								<TableCell class="text-sm tabular-nums"
									>{getRowStringValue(row, 'uom', columnMapping)}</TableCell
								>
								<TableCell class="text-right text-sm tabular-nums"
									>{formatCurrency(getRate(row))}</TableCell
								>
								<TableCell class="text-right text-sm tabular-nums"
									>{formatCurrency(getSubtotal(row))}</TableCell
								>
								<TableCell class="text-right text-sm tabular-nums">
									{getFactor(row).toLocaleString(undefined, {
										minimumFractionDigits: 1,
										maximumFractionDigits: 2,
									})}
								</TableCell>
								<TableCell class="text-right text-sm font-medium tabular-nums"
									>{formatCurrency(getTotal(row))}</TableCell
								>
							</TableRow>
						{/each}
					</TableBody>

					<!-- Table Footer with Totals -->
					<TableBody class="border-t-2">
						<TableRow class="bg-muted/50 font-medium">
							<TableCell colspan={7} class="text-right">
								<span class="text-sm">Total ({totalItems} items):</span>
							</TableCell>
							<TableCell class="text-right text-sm font-bold"
								>{formatCurrency(grandTotal)}</TableCell
							>
						</TableRow>
					</TableBody>
				</Table>
			</div>
		</CardContent>
	</Card>

	<div class="flex justify-between">
		<Button variant="outline" onclick={onBack} disabled={isImporting}>Back</Button>
		<Button
			onclick={handleImport}
			disabled={hasErrors || isImporting || totalItems === 0}
			class="min-w-32"
		>
			{#if isImporting}
				Importing...
			{:else}
				Import {totalItems} Items
			{/if}
		</Button>
	</div>
</div>
