<script lang="ts">
	import Logo from '$lib/assets/logo.svg.svelte';
	import NavMain from '$lib/components/nav-main.svelte';
	import NavUser from '$lib/components/nav-user.svelte';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';
	import {
		UserList as UserListIcon,
		Books as BooksIcon,
		HardHat as HardHatIcon,
	} from 'phosphor-svelte';
	import { Separator } from '$lib/components/ui/separator/index.js';
	import type { ComponentProps } from 'svelte';
	import { getCurrentOrgId } from '$lib/current-org.svelte';
	import OrgSwitcher from './org-switcher.svelte';
	import { page } from '$app/state';

	const sidebar = Sidebar.useSidebar();

	const profile = $derived(page.data.profile);
	const currentOrg = $derived(getCurrentOrgId().getCurrentOrg());

	let {
		ref = $bindable(null),
		collapsible = 'icon',
		clients = [],
		...restProps
	}: ComponentProps<typeof Sidebar.Root> & {
		clients?: { client_id: string; name: string; organization: { name: string } }[];
	} = $props();

	const navMain = $derived([
		{
			title: 'Clients',
			url: currentOrg ? `/org/${encodeURIComponent(currentOrg!.name)}/clients` : '/org/new',
			icon: UserListIcon,
			isActive: false,
			items: [
				{
					title: 'Add new client',
					url: currentOrg ? `/org/${encodeURIComponent(currentOrg!.name)}/clients/new` : '/org/new',
				},
			],
		},
		{
			title: 'WBS Libraries',
			url: '/wbs-libraries',
			icon: BooksIcon,
			isActive: false,
			items: [],
		},
		{
			title: 'Contractors',
			url: '/contractors',
			icon: HardHatIcon,
			isActive: false,
			items: [],
		},
	]);
</script>

<Sidebar.Root bind:ref {collapsible} {...restProps}>
	<Sidebar.Header>
		<!-- Logo and App Name -->
		{#if sidebar.open}
			<a href="/">
				<div class="flex items-center gap-2 px-4 py-3">
					<div class="shrink-0">
						<Logo />
					</div>
					<span class="text-2xl font-semibold text-nowrap">Cost Atlas</span>
				</div>
			</a>
			<div class="mx-auto min-h-12">
				<OrgSwitcher />
			</div>
		{:else}
			<a href="/">
				<div class="flex items-center pt-3 pb-1">
					<div class="shrink-0">
						<Logo />
					</div>
				</div>
			</a>
		{/if}
	</Sidebar.Header>
	<Separator />

	<Sidebar.Content>
		<NavMain items={navMain} {clients} />
	</Sidebar.Content>

	<Sidebar.Footer class={sidebar.open ? 'p-2' : 'px-px py-2'}>
		<Separator />
		<NavUser {profile} />
	</Sidebar.Footer>

	<Sidebar.Rail />
</Sidebar.Root>
