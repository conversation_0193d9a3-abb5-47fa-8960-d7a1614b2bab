<script lang="ts">
	import BudgetNode from '$lib/components/budget-node.svelte';
	import NotePencilIcon from 'phosphor-svelte/lib/NotePencil';
	import FloppyDiskIcon from 'phosphor-svelte/lib/FloppyDisk';
	import XIcon from 'phosphor-svelte/lib/X';
	import CaretDownIcon from 'phosphor-svelte/lib/CaretDown';
	import CaretRightIcon from 'phosphor-svelte/lib/CaretRight';
	import { formatCurrency, formatPercentage } from '$lib/utils';
	import { SvelteSet } from 'svelte/reactivity';
	import {
		calculateUnitRate,
		type BudgetLineItem,
		type EnhancedWbsItemTree,
	} from '$lib/budget_utils';
	import Button from '$lib/components/ui/button/button.svelte';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { superForm, type Infer, type SuperValidated } from 'sveltekit-superforms';
	import { slide } from 'svelte/transition';
	import { budgetItemSchema } from '$lib/schemas/project';
	import { toast } from 'svelte-sonner';

	let {
		node,
		indent,
		expanded,
		toggle,
		data,
		projectId,
	}: {
		node: EnhancedWbsItemTree;
		indent: number;
		expanded: SvelteSet<string>;
		toggle: (nodeId: string) => void;
		data: SuperValidated<Infer<typeof budgetItemSchema>>;
		projectId: string;
	} = $props();

	// ─── Derived reactive state ───
	// true if this node is in the expanded set
	const isOpen = $derived(!expanded.has(node.nodeId));
	const isEditing = new SvelteSet<BudgetLineItem['budget_line_item_id']>();

	// Called when the caret is clicked:
	function onToggle() {
		toggle(node.nodeId);
	}

	const form = superForm(data, {
		id: `budget-item-${node.nodeId}-${node.budgetItems[0]?.budget_line_item_id ?? '0'}`,
		warnings: {
			duplicateId: false,
		},
		onChange: async (event) => {
			if (
				event.paths.length === 1 &&
				[
					'unit_rate',
					'material_rate',
					'labor_rate',
					'productivity_per_hour',
					'unit_rate_manual_override',
				].includes(event.paths[0])
			) {
				$formData.unit_rate = calculateUnitRate($formData);
			}
		},
		onUpdated: async ({ form: f }) => {
			if (f.message) {
				if (f.message?.type === 'error') {
					toast.error(f.message?.text);
				} else if (f.message?.type === 'success') {
					toast.success(f.message?.text);
					cancelEditing(f.data.budget_line_item_id);
				}
			}
		},
	});

	const { form: formData, enhance } = form;

	function createEmptyBudgetItem(): BudgetLineItem {
		return {
			project_id: projectId,
			wbs_library_item_id: '',
			quantity: 0,
			unit: '',
			material_rate: 0,
			labor_rate: null,
			productivity_per_hour: null,
			unit_rate_manual_override: false,
			unit_rate: 0,
			remarks: null,
			cost_certainty: null,
			design_certainty: null,
		};
	}

	// Start editing a budget item
	function startEditing(item: BudgetLineItem) {
		$formData = { ...item };

		isEditing.add(item.budget_line_item_id);
	}

	// Cancel editing
	function cancelEditing(id: BudgetLineItem['budget_line_item_id']) {
		// Reset the form data
		$formData = createEmptyBudgetItem();

		isEditing.delete(id);
	}
</script>

<div
	class="group hover:bg-muted/20 has-[form]:bg-muted/50 relative col-span-[13] grid grid-cols-subgrid border-b py-3 pr-8 {node
		.children.length > 0
		? 'font-medium'
		: ''}"
>
	<div class="col-span-1 flex items-center px-2">
		{#if node.children.length > 0}
			<button onclick={onToggle} aria-label={isOpen ? 'Collapse' : 'Expand'}>
				{#if isOpen}
					<CaretDownIcon class="size-4" />
				{:else}
					<CaretRightIcon class="size-4" />
				{/if}
			</button>
			<span class="ml-2">{node.code}</span>
		{:else}
			<span class="ml-6">{node.code}</span>
		{/if}
	</div>

	<div class="col-span-2 px-2" style="padding-left: {0.5 + indent * 0.75}rem">
		{node.description}
	</div>
	{#each node.budgetItems as budgetItem (budgetItem.budget_line_item_id)}
		{#if isEditing.has(budgetItem.budget_line_item_id)}
			<form
				action="?/upsertBudgetItem"
				method="post"
				use:enhance
				class="col-span-10 grid grid-cols-subgrid items-center pr-8"
			>
				<input type="hidden" name="project_id" value={projectId} />
				<input type="hidden" name="budget_line_item_id" value={budgetItem.budget_line_item_id} />
				<div class="col-span-1 px-0">
					<Form.Field {form} name="quantity" class="col-span-1 px-0">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Quantity</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									bind:value={$formData.quantity}
									placeholder="Quantity"
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>
				<div class="col-span-1 px-0">
					<Form.Field {form} name="unit">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Unit</Form.Label>
								<Input {...props} type="text" bind:value={$formData.unit} placeholder="Unit" />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>
				<div class="col-span-1 px-0">
					<Form.Field {form} name="material_rate">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Material Rate</Form.Label>
								<Input
									{...props}
									type="number"
									placeholder="Material Rate"
									step="0.01"
									bind:value={$formData.material_rate}
									disabled={$formData.unit_rate_manual_override}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>
				<div class="col-span-1 px-0">
					<Form.Field {form} name="labor_rate">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Labor Rate</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									placeholder="0"
									bind:value={$formData.labor_rate}
									disabled={$formData.unit_rate_manual_override}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>
				<div class="col-span-1 px-0">
					<Form.Field {form} name="productivity_per_hour">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Productivity (qty/hour)</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									bind:value={$formData.productivity_per_hour}
									disabled={$formData.unit_rate_manual_override}
									placeholder="0"
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>
				<div class="relative col-span-1 px-0">
					<div class="flex items-center gap-2">
						<Form.Field {form} name="unit_rate">
							<Form.Control>
								{#snippet children({ props })}
									<Form.Label class="sr-only">
										{$formData.unit_rate_manual_override
											? 'Manual Unit Rate'
											: 'Calculated Unit Rate'}
									</Form.Label>
									<Input
										{...props}
										type="number"
										step="0.01"
										bind:value={$formData.unit_rate}
										disabled={!$formData.unit_rate_manual_override}
										placeholder="Unit Rate"
									/>
								{/snippet}
							</Form.Control>
							<Form.FieldErrors />
						</Form.Field>
					</div>

					<Form.Field
						{form}
						name="unit_rate_manual_override"
						class="absolute -right-1 -bottom-8 z-10 flex min-w-max items-center gap-2"
					>
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Manual Unit Rate</Form.Label>
								<Checkbox {...props} bind:checked={$formData.unit_rate_manual_override} />
							{/snippet}
						</Form.Control>
					</Form.Field>
				</div>

				<div class="col-span-1 px-0">
					<Form.Field {form} name="factor">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Factor</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									bind:value={$formData.factor}
									placeholder="1"
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="col-span-1 flex items-center justify-end px-2">
					{formatCurrency($formData.quantity * ($formData.unit_rate || 0))}
				</div>
				<div class="col-span-1 px-0">
					<Form.Field {form} name="cost_certainty">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Cost Certainty</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									placeholder="%"
									bind:value={$formData.cost_certainty}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>
				<div class="col-span-1 px-0">
					<Form.Field {form} name="design_certainty">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label class="sr-only">Design Certainty</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									placeholder="%"
									bind:value={$formData.design_certainty}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>
				<div class="col-span-10 grid grid-cols-subgrid pr-8 pb-2" in:slide={{ duration: 300 }}>
					<div class="col-span-8"></div>
					<div class="col-span-1">
						<Button
							variant="outline"
							class="mx-1 w-full"
							onclick={() => cancelEditing(budgetItem.budget_line_item_id)}
						>
							<XIcon class="mr-1 size-4" />
							Cancel
						</Button>
					</div>
					<div class="col-span-1">
						<Button class="mx-1 w-full" type="submit">
							<FloppyDiskIcon class="size-4" />
							Save
						</Button>
					</div>
				</div>
			</form>
		{:else}
			<div class="col-span-[11] col-start-4 grid grid-cols-subgrid">
				<!-- Quantity, Unit -->
				<div class="col-span-1 px-2 text-right">
					{budgetItem.quantity}
				</div>
				<div class="col-span-1 px-2">
					{budgetItem.unit ?? ''}
				</div>

				<!-- Rate Calculation -->
				<div class="col-span-1 px-2 text-right">
					{#if !budgetItem.unit_rate_manual_override}
						{formatCurrency(budgetItem.material_rate)}
					{/if}
				</div>
				<div class="col-span-1 px-2 text-right">
					{#if !budgetItem.unit_rate_manual_override}
						{formatCurrency(budgetItem.labor_rate ?? 0)}
					{/if}
				</div>
				<div class="col-span-1 px-2 text-right">
					{#if !budgetItem.unit_rate_manual_override}
						{formatCurrency(budgetItem.productivity_per_hour ?? 0)}
					{/if}
				</div>
				<div class="col-span-1 px-2 text-right">
					{formatCurrency(budgetItem.unit_rate)}
				</div>

				<div class="col-span-1 px-2 text-right">
					{budgetItem.factor ?? ''}
				</div>

				<!-- Subtotal, Certainties -->
				<div class="col-span-1 px-2 text-right">
					{formatCurrency(budgetItem.quantity * budgetItem.unit_rate * (budgetItem.factor ?? 1))}
				</div>
				<div class="col-span-1 px-2 text-center">
					{budgetItem.cost_certainty != null ? formatPercentage(budgetItem.cost_certainty) : ''}
				</div>
				<div class="col-span-1 px-2 text-center">
					{budgetItem.design_certainty != null ? formatPercentage(budgetItem.design_certainty) : ''}
				</div>
			</div>
			<div
				class="absolute top-1 right-0 z-10 flex gap-1 text-black opacity-0 group-hover:opacity-100"
			>
				<Button variant="ghost" size="icon" onclick={() => startEditing(budgetItem)}>
					<NotePencilIcon class="size-3" />
				</Button>
			</div>
		{/if}
	{:else}
		<div class="grid grid-cols-subgrid col-start-4 col-span-[11]">
			<div class="col-span-7"></div>
			<div class="col-span-1 px-2 text-right">{formatCurrency(node.totalCost)}</div>
			<div class="col-span-1 px-2 text-right"></div>
			<div class="col-span-1 px-2 text-right"></div>
		</div>
	{/each}
</div>

<!-- Only render children when this node is open -->
{#if isOpen}
	{#each node.children as child (child.nodeId)}
		<BudgetNode node={child} indent={indent + 1} {expanded} {toggle} {data} {projectId} />
	{/each}
{/if}

<style>
	.col-span-1 {
		align-self: start;
		justify-self: stretch;
		width: 6rem;
		/* width: 100%; */
	}

	.col-span-2 {
		align-self: start;
		width: 12rem;
		/* width: 100%; */
	}
</style>
