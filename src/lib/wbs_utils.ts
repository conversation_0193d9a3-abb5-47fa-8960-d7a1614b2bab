import type { WbsItemTree, WbsLibraryItemWithId } from './schemas/wbs';

/**
 * Converts a flat array of WBS items to a hierarchical tree structure
 * @param items Array of WBS items with IDs
 * @returns Array of root WBS items with children arrays
 */
export function buildWbsItemTree(items: WbsLibraryItemWithId[]): WbsItemTree[] {
	const itemMap = new Map<string, WbsItemTree>();
	const rootItems: WbsItemTree[] = [];

	// First pass: Create all nodes with empty children arrays
	items.forEach((item) => {
		itemMap.set(item.wbs_library_item_id, { ...item, children: [] });
	});

	// Second pass: Create parent-child relationships for all levels
	items.forEach((item) => {
		const node = itemMap.get(item.wbs_library_item_id);

		if (!node) return;

		if (item.parent_item_id === null || item.parent_item_id === undefined) {
			// This is a root item
			rootItems.push(node);
		} else {
			// Add this item to its parent's children
			const parent = itemMap.get(item.parent_item_id);
			if (parent) {
				parent.children.push(node);
			} else {
				// If parent doesn't exist (which shouldn't happen with valid data),
				// add to root items as a fallback
				rootItems.push(node);
			}
		}
	});

	// Sort children by code at each level
	const sortNodeChildren = (nodes: WbsItemTree[]) => {
		// Sort current level
		nodes.sort((a, b) => a.code.localeCompare(b.code, undefined, { numeric: true }));

		// Recursively sort children
		for (const node of nodes) {
			if (node.children.length > 0) {
				sortNodeChildren(node.children);
			}
		}
	};

	// Sort the entire tree
	sortNodeChildren(rootItems);

	return rootItems;
}
