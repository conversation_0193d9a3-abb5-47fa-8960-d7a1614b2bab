/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createMockSupabaseClient } from '$tests/mocks/supabase';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { changePasswordSchema, resetPasswordSchema } from '$lib/schemas/auth';

// Mock SvelteKit modules
vi.mock('@sveltejs/kit', async () => {
	const original = await vi.importActual('@sveltejs/kit');
	return {
		...original,
		fail: vi.fn().mockImplementation((code, data) => ({ status: code, ...data })),
		redirect: vi.fn().mockImplementation((status, location) => {
			const error = new Error(`Redirect to ${location}`);
			error.status = status;
			error.location = location;
			throw error;
		}),
	};
});

// Mock the superforms library
vi.mock('sveltekit-superforms/server', async () => {
	return {
		superValidate: vi.fn().mockImplementation(async (request, validator) => {
			// If request is an object with data property, treat it as pre-validated form
			if (request && typeof request === 'object' && 'data' in request) {
				return request;
			}

			// Otherwise assume it's a standard request
			const formData = await request?.formData?.();
			const data = Object.fromEntries(formData?.entries() || []);
			const validation = validator.safeParse(data);

			return {
				valid: validation.success,
				data: validation.success ? validation.data : data,
				errors: validation.success ? {} : validation.error.flatten().fieldErrors,
			};
		}),
	};
});

// Mock the superforms adapter
vi.mock('sveltekit-superforms/adapters', () => {
	return {
		zod: vi.fn().mockImplementation((schema) => schema),
	};
});

// Minimal event shape
interface TestEvent {
	request: Request;
	locals: {
		supabase: ReturnType<typeof createMockSupabaseClient>;
	};
}

describe('Password Reset Flow Integration', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	describe('Reset Password Request', () => {
		it('should successfully send a password reset email', async () => {
			// Setup superValidate mock to return valid form
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: true,
				data: { email: '<EMAIL>' },
				errors: {},
			} as unknown);

			// Mock form data
			const formData = new FormData();
			formData.append('email', '<EMAIL>');

			// Mock request
			const request = new Request('http://test', { method: 'POST', body: formData });
			const event: TestEvent = {
				request,
				locals: {
					supabase: createMockSupabaseClient({
						resetPasswordForEmail: { data: {}, error: null },
					}),
				},
			};

			// Mock implementation of reset password action
			const mockAction = async () => {
				const form = await superValidate(event.request, zod(resetPasswordSchema));

				if (!form.valid) {
					return { form };
				}

				const { error } = await event.locals.supabase.auth.resetPasswordForEmail(form.data.email);

				if (error) {
					return { form, success: false };
				}

				return { form, success: true };
			};

			// Run the action
			const result = await mockAction();

			// Verify the result
			expect(result.success).toBe(true);

			// Verify Supabase was called with correct parameters
			expect(event.locals.supabase.auth.resetPasswordForEmail).toHaveBeenCalledWith(
				'<EMAIL>',
			);
		});

		it('should handle non-existent email gracefully', async () => {
			// Setup superValidate mock to return valid form
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: true,
				data: { email: '<EMAIL>' },
				errors: {},
			} as unknown);

			// Mock form data with non-existent email
			const formData = new FormData();
			formData.append('email', '<EMAIL>');

			// Mock request
			const request = new Request('http://test', { method: 'POST', body: formData });
			const event: TestEvent = {
				request,
				locals: {
					supabase: createMockSupabaseClient({
						resetPasswordForEmail: { data: {}, error: null },
					}),
				},
			};

			// Mock implementation of reset password action
			const mockAction = async () => {
				const form = await superValidate(event.request, zod(resetPasswordSchema));

				if (!form.valid) {
					return { form };
				}

				const { error } = await event.locals.supabase.auth.resetPasswordForEmail(form.data.email);

				if (error) {
					return { form, success: false };
				}

				return { form, success: true };
			};

			// Run the action
			const result = await mockAction();

			// Verify the result (should still indicate success for security reasons)
			expect(result.success).toBe(true);
		});

		it('should validate email format', async () => {
			// Setup superValidate mock to return invalid form
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: false,
				data: { email: 'invalid-email' },
				errors: { email: ['Invalid email address'] },
			} as unknown);

			// Mock form data with invalid email
			const formData = new FormData();
			formData.append('email', 'invalid-email');

			// Mock request
			const request = new Request('http://test', { method: 'POST', body: formData });
			const event: TestEvent = {
				request,
				locals: {
					supabase: createMockSupabaseClient(),
				},
			};

			// Mock implementation of reset password action
			const mockAction = async () => {
				const form = await superValidate(event.request, zod(resetPasswordSchema));

				if (!form.valid) {
					return { form };
				}

				const { error } = await event.locals.supabase.auth.resetPasswordForEmail(form.data.email);

				if (error) {
					return { form, success: false };
				}

				return { form, success: true };
			};

			// Run the action
			const result = await mockAction();

			// Verify validation fails
			expect(result.form.valid).toBe(false);
			expect(result.form.errors.email).toContain('Invalid email address');

			// Verify Supabase was not called
			expect(event.locals.supabase.auth.resetPasswordForEmail).not.toHaveBeenCalled();
		});
	});

	describe('Change Password', () => {
		it('should successfully update password', async () => {
			// Setup superValidate mock to return valid form
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: true,
				data: {
					password: 'NewPassword123',
					confirmPassword: 'NewPassword123',
				},
				errors: {},
			} as unknown);

			// Mock form data
			const formData = new FormData();
			formData.append('password', 'NewPassword123');
			formData.append('confirmPassword', 'NewPassword123');

			// Mock request
			const request = new Request('http://test', { method: 'POST', body: formData });
			const event: TestEvent = {
				request,
				locals: {
					supabase: createMockSupabaseClient({
						updateUser: { data: { user: { id: 'user123' } }, error: null },
					}),
				},
			};

			// Mock implementation of change password action
			const mockAction = async () => {
				const form = await superValidate(event.request, zod(changePasswordSchema));

				if (!form.valid) {
					return { form };
				}

				const { error } = await event.locals.supabase.auth.updateUser({
					password: form.data.password,
				});

				if (error) {
					return { form, error: error.message };
				}

				return { form, success: true };
			};

			// Run the action
			const result = await mockAction();

			// Verify the result
			expect(result.success).toBe(true);

			// Verify Supabase was called with correct parameters
			expect(event.locals.supabase.auth.updateUser).toHaveBeenCalledWith({
				password: 'NewPassword123',
			});
		});

		it('should reject mismatched passwords', async () => {
			// Setup superValidate mock to return invalid form
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: false,
				data: {
					password: 'Password123',
					confirmPassword: 'DifferentPassword123',
				},
				errors: { confirmPassword: ["Passwords don't match"] },
			} as unknown);

			// Mock form data with mismatched passwords
			const formData = new FormData();
			formData.append('password', 'Password123');
			formData.append('confirmPassword', 'DifferentPassword123');

			// Mock request
			const request = new Request('http://test', { method: 'POST', body: formData });
			const event: TestEvent = {
				request,
				locals: {
					supabase: createMockSupabaseClient(),
				},
			};

			// Mock implementation of change password action
			const mockAction = async () => {
				const form = await superValidate(event.request, zod(changePasswordSchema));

				if (!form.valid) {
					return { form };
				}

				const { error } = await event.locals.supabase.auth.updateUser({
					password: form.data.password,
				});

				if (error) {
					return { form, error: error.message };
				}

				return { form, success: true };
			};

			// Run the action
			const result = await mockAction();

			// Verify validation fails
			expect(result.form.valid).toBe(false);
			expect(result.form.errors.confirmPassword).toContain("Passwords don't match");

			// Verify Supabase was not called
			expect(event.locals.supabase.auth.updateUser).not.toHaveBeenCalled();
		});

		it('should handle weak password errors from Supabase', async () => {
			// Setup superValidate mock to return valid form
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: true,
				data: {
					password: 'password123',
					confirmPassword: 'password123',
				},
				errors: {},
			} as unknown);

			// Mock form data
			const formData = new FormData();
			formData.append('password', 'password123');
			formData.append('confirmPassword', 'password123');

			// Mock request
			const request = new Request('http://test', { method: 'POST', body: formData });
			const event: TestEvent = {
				request,
				locals: {
					supabase: createMockSupabaseClient({
						updateUser: {
							data: {},
							error: { message: 'Password does not meet minimum requirements' },
						},
					}),
				},
			};

			// Mock implementation of change password action
			const mockAction = async () => {
				const form = await superValidate(event.request, zod(changePasswordSchema));

				if (!form.valid) {
					return { form };
				}

				const { error } = await event.locals.supabase.auth.updateUser({
					password: form.data.password,
				});

				if (error) {
					return { form, error: error.message };
				}

				return { form, success: true };
			};

			// Run the action
			const result = await mockAction();

			// Verify the error message
			expect(result.error).toBe('Password does not meet minimum requirements');
		});
	});
});
