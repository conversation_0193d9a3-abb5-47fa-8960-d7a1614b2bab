import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createMockSupabaseClient } from '../../../tests/mocks/supabase';
import { redirect } from '@sveltejs/kit';

// Mock the redirect function
vi.mock('@sveltejs/kit', async (importOriginal: () => Promise<unknown>) => {
	const actual = (await importOriginal()) as Record<string, unknown>;
	return {
		...actual,
		redirect: vi.fn().mockImplementation((status, location) => {
			throw { status, location };
		}),
	};
});

describe('Sign Out Functionality', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	// Create a mock signout handler similar to what might be in the app
	const mockSignOutHandler = async ({
		locals,
		cookies,
	}: {
		locals: { supabase: ReturnType<typeof createMockSupabaseClient> };
		cookies?: { delete: (key: string) => void };
	}) => {
		const { error } = await locals.supabase.auth.signOut();

		if (error) {
			return { success: false, error: error.message };
		}

		// Clear organization cookie
		if (cookies?.delete) {
			cookies.delete('activeOrgId');
		}

		// In a real implementation, this would redirect to login
		return { success: true, redirectTo: '/auth/signin' };
	};

	it('should successfully sign out a user', async () => {
		// Mock Supabase client with successful signout
		const mockSupabase = createMockSupabaseClient({
			signOut: { error: null },
		});

		// Mock cookies
		const mockCookies = {
			delete: vi.fn(),
		};

		const locals = { supabase: mockSupabase };

		// Call the signout handler
		const result = await mockSignOutHandler({ locals, cookies: mockCookies });

		// Verify successful signout
		expect(result.success).toBe(true);
		expect(result.redirectTo).toBe('/auth/signin');

		// Verify Supabase was called
		expect(mockSupabase.auth.signOut).toHaveBeenCalled();

		// Verify organization cookie was deleted
		expect(mockCookies.delete).toHaveBeenCalledWith('activeOrgId');
	});

	it('should handle signout errors', async () => {
		// Mock Supabase client with signout error
		const mockSupabase = createMockSupabaseClient({
			signOut: { error: { message: 'Network error' } },
		});

		const locals = { supabase: mockSupabase };

		// Call the signout handler
		const result = await mockSignOutHandler({ locals, cookies: undefined });

		// Verify failed signout
		expect(result.success).toBe(false);
		expect(result.error).toBe('Network error');
	});

	it('should work with actual route handlers and redirection', async () => {
		// Mock Supabase client with successful signout
		const mockSupabase = createMockSupabaseClient({
			signOut: { error: null },
		});

		// Create a more realistic route handler with redirection
		const signOutRouteHandler = async ({
			locals,
			cookies,
		}: {
			locals: { supabase: ReturnType<typeof createMockSupabaseClient> };
			cookies?: { delete: (key: string) => void };
		}) => {
			const { error } = await locals.supabase.auth.signOut();

			if (error) {
				// In a real app, this might return a form with an error message
				return { error: error.message };
			}

			// Clear organization cookie
			if (cookies?.delete) {
				cookies.delete('activeOrgId');
			}

			// Redirect to login page
			return redirect(303, '/auth/signin');
		};

		const locals = { supabase: mockSupabase };
		const mockCookies = { delete: vi.fn() };

		// Expect redirect to be thrown
		try {
			await signOutRouteHandler({ locals, cookies: mockCookies });
			// If we get here, the test should fail
			expect('This should not be reached').toBe(false);
		} catch (err) {
			expect(err).toEqual({ status: 303, location: '/auth/signin' });
		}

		// Verify Supabase was called
		expect(mockSupabase.auth.signOut).toHaveBeenCalled();

		// Verify organization cookie was deleted
		expect(mockCookies.delete).toHaveBeenCalledWith('activeOrgId');
	});

	it('should clear local storage on signout', async () => {
		// Create a mock browser storage implementation for testing
		const mockLocalStorage = (() => {
			const store: Record<string, string> = {
				'supabase.auth.token': JSON.stringify({
					currentSession: { access_token: 'test-token' },
				}),
			};
			return {
				getItem: vi.fn((key: string) => store[key] || null),
				setItem: vi.fn((key: string, value: string) => {
					store[key] = value;
				}),
				removeItem: vi.fn((key: string) => {
					delete store[key];
				}),
				clear: vi.fn(() => {
					Object.keys(store).forEach((key) => delete store[key]);
				}),
			};
		})();

		// Mock window.localStorage
		const originalLocalStorage = global.localStorage;
		Object.defineProperty(global, 'localStorage', {
			value: mockLocalStorage,
			writable: true,
		});

		try {
			// Mock Supabase client with successful signout
			const mockSupabase = createMockSupabaseClient({
				signOut: { error: null },
			});

			// Mock cookies
			const mockCookies = {
				delete: vi.fn(),
			};

			const locals = { supabase: mockSupabase };

			// Verify token exists in storage before signout
			expect(mockLocalStorage.getItem('supabase.auth.token')).toBeTruthy();

			// Call the signout handler
			await mockSignOutHandler({ locals, cookies: mockCookies });

			// Simulate what Supabase client would do - remove the token
			mockLocalStorage.removeItem('supabase.auth.token');

			// Verify local storage item was removed
			expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('supabase.auth.token');
			expect(mockLocalStorage.getItem('supabase.auth.token')).toBeNull();
		} finally {
			// Restore the original localStorage
			Object.defineProperty(global, 'localStorage', {
				value: originalLocalStorage,
				writable: true,
			});
		}
	});
});
