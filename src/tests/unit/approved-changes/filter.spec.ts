import { describe, it, expect } from 'vitest';
import { approvedChangeStatuses, approvedChangesFilterSchema } from '$lib/schemas/risk';

describe('Approved Changes Filter Tests', () => {
	describe('Filter Schema', () => {
		it('should have correct approved change statuses', () => {
			expect(approvedChangeStatuses).toEqual(['approved', 'implemented', 'closed']);
		});

		it('should validate filter schema with valid data', () => {
			const validData = {
				status: 'approved',
				date_from: '2024-01-01',
				date_to: '2024-12-31',
				wbs_library_item_id: '550e8400-e29b-41d4-a716-446655440000',
			};

			const result = approvedChangesFilterSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should validate filter schema with all status', () => {
			const validData = {
				status: 'all',
				date_from: undefined,
				date_to: undefined,
				wbs_library_item_id: undefined,
			};

			const result = approvedChangesFilterSchema.safeParse(validData);
			expect(result.success).toBe(true);
		});

		it('should reject invalid status', () => {
			const invalidData = {
				status: 'invalid-status',
			};

			const result = approvedChangesFilterSchema.safeParse(invalidData);
			expect(result.success).toBe(false);
		});
	});

	describe('URL Parameter Handling', () => {
		it('should parse status filter from URL', () => {
			const url = new URL('http://localhost:5173/test?status=approved');
			const statusFilter = url.searchParams.get('status') || 'all';
			expect(statusFilter).toBe('approved');
		});

		it('should parse date filters from URL', () => {
			const url = new URL('http://localhost:5173/test?date_from=2024-01-01&date_to=2024-12-31');
			const dateFromFilter = url.searchParams.get('date_from');
			const dateToFilter = url.searchParams.get('date_to');
			expect(dateFromFilter).toBe('2024-01-01');
			expect(dateToFilter).toBe('2024-12-31');
		});

		it('should parse WBS item filter from URL', () => {
			const url = new URL('http://localhost:5173/test?wbs_item=wbs-123');
			const wbsItemFilter = url.searchParams.get('wbs_item');
			expect(wbsItemFilter).toBe('wbs-123');
		});

		it('should handle multiple filters in URL', () => {
			const url = new URL(
				'http://localhost:5173/test?status=approved&date_from=2024-01-01&wbs_item=wbs-123',
			);
			const statusFilter = url.searchParams.get('status');
			const dateFromFilter = url.searchParams.get('date_from');
			const wbsItemFilter = url.searchParams.get('wbs_item');

			expect(statusFilter).toBe('approved');
			expect(dateFromFilter).toBe('2024-01-01');
			expect(wbsItemFilter).toBe('wbs-123');
		});

		it('should default to "all" when no status filter is provided', () => {
			const url = new URL('http://localhost:5173/test');
			const statusFilter = url.searchParams.get('status') || 'all';
			expect(statusFilter).toBe('all');
		});
	});

	describe('Filter URL Building', () => {
		it('should build URL parameters correctly', () => {
			const params = new URLSearchParams();

			const filterData = {
				status: 'approved',
				date_from: '2024-01-01',
				date_to: '2024-12-31',
				wbs_library_item_id: 'wbs-123',
			};

			if (filterData.status && filterData.status !== 'all') {
				params.set('status', filterData.status);
			}
			if (filterData.date_from) {
				params.set('date_from', filterData.date_from);
			}
			if (filterData.date_to) {
				params.set('date_to', filterData.date_to);
			}
			if (filterData.wbs_library_item_id) {
				params.set('wbs_item', filterData.wbs_library_item_id);
			}

			const urlString = params.toString();
			expect(urlString).toContain('status=approved');
			expect(urlString).toContain('date_from=2024-01-01');
			expect(urlString).toContain('date_to=2024-12-31');
			expect(urlString).toContain('wbs_item=wbs-123');
		});

		it('should skip "all" status in URL parameters', () => {
			const params = new URLSearchParams();

			const filterData = {
				status: 'all',
				date_from: '2024-01-01',
			};

			if (filterData.status && filterData.status !== 'all') {
				params.set('status', filterData.status);
			}
			if (filterData.date_from) {
				params.set('date_from', filterData.date_from);
			}

			const urlString = params.toString();
			expect(urlString).not.toContain('status=all');
			expect(urlString).toContain('date_from=2024-01-01');
		});
	});
});
