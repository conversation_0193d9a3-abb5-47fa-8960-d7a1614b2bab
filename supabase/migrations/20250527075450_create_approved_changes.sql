-- Create approved changes table (mirrors risk_register but excludes probability field)
CREATE TABLE IF NOT EXISTS "public"."approved_changes" (
	approved_change_id SERIAL PRIMARY KEY,
	project_id UUID NOT NULL REFERENCES project (project_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
	title TEXT NOT NULL,
	description TEXT NOT NULL,
	status TEXT NOT NULL DEFAULT 'approved',
	wbs_library_item_id UUID REFERENCES wbs_library_item (wbs_library_item_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
	date_identified DATE NOT NULL DEFAULT CURRENT_DATE,
	date_approved DATE NOT NULL DEFAULT CURRENT_DATE,
	cause TEXT,
	effect TEXT,
	program_impact TEXT,
	-- Note: No probability field since approved changes are 100% certain
	potential_impact NUMERIC(15, 2),
	mitigation_plan TEXT,
	date_for_review DATE,
	risk_owner_user_id UUID REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
	risk_owner_name TEXT,
	risk_owner_email TEXT,
	approved_by_user_id UUID REFERENCES profile (user_id) ON UPDATE RESTRICT ON DELETE RESTRICT,
	original_risk_id INT REFERENCES risk_register (risk_id) ON UPDATE RESTRICT ON DELETE SET NULL,
	created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
	updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
	-- Ensure you never have both a user_id and a free-text owner
	CONSTRAINT one_owner_type CHECK (
		(risk_owner_user_id IS NOT NULL)::int + (
			(risk_owner_name IS NOT NULL)::int + (risk_owner_email IS NOT NULL)::int
		) <= 1
	)
);

-- Add comment to the table
COMMENT ON TABLE "public"."approved_changes" IS 'Approved changes register for tracking approved project changes that were previously high-likelihood risks';

-- Add comments to columns
COMMENT ON COLUMN "public"."approved_changes"."approved_change_id" IS 'Primary key for the approved changes register';

COMMENT ON COLUMN "public"."approved_changes"."project_id" IS 'The project this approved change belongs to';

COMMENT ON COLUMN "public"."approved_changes"."title" IS 'Short title for the approved change';

COMMENT ON COLUMN "public"."approved_changes"."description" IS 'Detailed description of the approved change';

COMMENT ON COLUMN "public"."approved_changes"."status" IS 'Current status of the approved change (approved, implemented, closed)';

COMMENT ON COLUMN "public"."approved_changes"."wbs_library_item_id" IS 'Optional link to a specific WBS item';

COMMENT ON COLUMN "public"."approved_changes"."date_identified" IS 'Date the original risk was identified';

COMMENT ON COLUMN "public"."approved_changes"."date_approved" IS 'Date the change was approved';

COMMENT ON COLUMN "public"."approved_changes"."cause" IS 'How the change arose';

COMMENT ON COLUMN "public"."approved_changes"."effect" IS 'Impact of the approved change';

COMMENT ON COLUMN "public"."approved_changes"."program_impact" IS 'Impact on project schedule';

COMMENT ON COLUMN "public"."approved_changes"."potential_impact" IS 'Financial impact of the approved change';

COMMENT ON COLUMN "public"."approved_changes"."mitigation_plan" IS 'Steps to implement or manage the approved change';

COMMENT ON COLUMN "public"."approved_changes"."date_for_review" IS 'When to revisit or reassess this approved change';

COMMENT ON COLUMN "public"."approved_changes"."risk_owner_user_id" IS 'User responsible for implementing the approved change';

COMMENT ON COLUMN "public"."approved_changes"."risk_owner_name" IS 'Name of external change owner (if not a system user)';

COMMENT ON COLUMN "public"."approved_changes"."risk_owner_email" IS 'Email of external change owner (if not a system user)';

COMMENT ON COLUMN "public"."approved_changes"."approved_by_user_id" IS 'User who approved the change';

COMMENT ON COLUMN "public"."approved_changes"."original_risk_id" IS 'Reference to the original risk that was approved';

-- Create trigger to update the updated_at timestamp
CREATE TRIGGER update_approved_changes_updated_at BEFORE
UPDATE ON public.approved_changes FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column ();

-- Enable Row Level Security
ALTER TABLE approved_changes ENABLE ROW LEVEL SECURITY;

-- Grant access to service_role
GRANT
SELECT
,
	INSERT,
UPDATE,
DELETE,
REFERENCES,
TRIGGER,
TRUNCATE ON public.approved_changes TO service_role;

-- Setup RLS policies following the same pattern as risk_register
-- Users can view approved changes for projects they have access to
CREATE POLICY "Users can view approved changes for projects they have access to" ON public.approved_changes FOR
SELECT
	TO authenticated USING (public.can_access_project (project_id));

-- Project Editors and Owners can insert approved changes
CREATE POLICY "Project Editors and Owners can insert approved changes" ON public.approved_changes FOR INSERT TO authenticated
WITH
	CHECK (
		public.current_user_has_entity_role ('project', project_id, 'editor')
	);

-- Project Editors and Owners can update approved changes
CREATE POLICY "Project Editors and Owners can update approved changes" ON public.approved_changes
FOR UPDATE
	TO authenticated USING (
		public.current_user_has_entity_role ('project', project_id, 'editor')
	)
WITH
	CHECK (
		public.current_user_has_entity_role ('project', project_id, 'editor')
	);

-- Project Owners can delete approved changes
CREATE POLICY "Project Owners can delete approved changes" ON public.approved_changes FOR DELETE TO authenticated USING (
	public.current_user_has_entity_role ('project', project_id, 'owner')
);

-- Create index for faster RLS policy evaluation
CREATE INDEX IF NOT EXISTS approved_changes_project_idx ON public.approved_changes USING btree (project_id);

-- Create index for original risk reference
CREATE INDEX IF NOT EXISTS approved_changes_original_risk_idx ON public.approved_changes USING btree (original_risk_id);
